import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shamsi_date/shamsi_date.dart';
import 'package:tailor_flutter/features/orders/domain/models/order.dart';
import 'package:tailor_flutter/core/theme/colors.dart'; // Import AppColors for statusPending

class OrderListItem extends StatelessWidget {
  final Order order;
  final VoidCallback onTap;

  const OrderListItem({
    super.key,
    required this.order,
    required this.onTap,
  });

  // Helper to get status properties using Theme and AppColors
  ({Color color, String text, IconData icon}) _getStatusProperties(BuildContext context, OrderStatus status) {
    final theme = Theme.of(context);
    switch (status) {
      case OrderStatus.pending:
        // Use defined status color or a specific color if not in theme
        return (color: AppColors.statusPending, text: 'در انتظار', icon: Icons.pending_actions_rounded);
      case OrderStatus.inProgress:
        return (color: theme.colorScheme.primary, text: 'در حال انجام', icon: Icons.construction_rounded);
      case OrderStatus.completed:
        // Assuming tertiary is mapped to success color in theme
        return (color: theme.colorScheme.tertiary, text: 'تکمیل شده', icon: Icons.check_circle_outline_rounded);
      case OrderStatus.delivered:
        // Use a specific color for delivered status
        return (color: Colors.purple.shade600, text: 'تحویل داده شده', icon: Icons.local_shipping_outlined);
      case OrderStatus.cancelled:
        return (color: theme.colorScheme.error, text: 'لغو شده', icon: Icons.cancel_outlined);
    }
  }

  String _formatPersianDateShort(DateTime date) {
      final jalali = Jalali.fromDateTime(date);
      final formatter = jalali.formatter;
      // Short format like yyyy/mm/dd
      return '${formatter.yyyy}/${formatter.mm}/${formatter.dd}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final currencyFormat = NumberFormat.currency(locale: 'fa_AF', symbol: '؋', decimalDigits: 0);
    final statusProps = _getStatusProperties(context, order.status);

    return Card(
      elevation: 1,
      // margin: const EdgeInsets.only(bottom: 12), // Let ListView.separated handle spacing
      shape: RoundedRectangleBorder(
         borderRadius: BorderRadius.circular(12),
         side: BorderSide(color: theme.dividerColor.withOpacity(0.5))
      ),
      clipBehavior: Clip.antiAlias, // Ensures InkWell respects border radius
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top Row: Customer Name and Status Chip
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer Info
                  Expanded(
                     child: Row(
                       children: [
                         CircleAvatar(
                           radius: 18,
                           backgroundColor: theme.colorScheme.secondaryContainer,
                           child: Icon(Icons.person_outline_rounded, size: 18, color: theme.colorScheme.onSecondaryContainer),
                         ),
                         const SizedBox(width: 12),
                         Flexible(
                           child: Text(
                             order.customer?.name ?? 'مشتری نامشخص',
                             style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                             overflow: TextOverflow.ellipsis,
                           ),
                         ),
                       ],
                     ),
                   ),
                  const SizedBox(width: 8),
                  // Status Chip
                  Chip(
                    avatar: Icon(statusProps.icon, size: 16, color: statusProps.color),
                    label: Text(
                      statusProps.text,
                      style: textTheme.labelMedium?.copyWith(color: statusProps.color, fontWeight: FontWeight.w500),
                    ),
                    backgroundColor: statusProps.color.withOpacity(0.1),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    side: BorderSide(color: statusProps.color.withOpacity(0.3)),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Middle Row: Receipt Number and Order Date
              Row(
                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                 children: [
                   Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                         Icon(Icons.receipt_long_outlined, size: 16, color: theme.colorScheme.onSurfaceVariant),
                         const SizedBox(width: 6),
                         Text(
                           'رسید #${order.receiptNumber ?? '-'}',
                           style: textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                         ),
                      ],
                   ),
                   Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                         Icon(Icons.calendar_today_outlined, size: 16, color: theme.colorScheme.onSurfaceVariant),
                         const SizedBox(width: 6),
                         Text(
                           _formatPersianDateShort(order.orderDate),
                           style: textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                         ),
                      ],
                   ),
                 ],
              ),
              const Divider(height: 24, thickness: 0.5),
              // Bottom Row: Payment Status and Price
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Payment Status Chip
                  Chip(
                    avatar: Icon(
                      order.isPaid ? Icons.check_circle_rounded : Icons.cancel_rounded,
                      size: 16,
                      // Use theme colors for consistency
                      color: order.isPaid ? theme.colorScheme.tertiary : theme.colorScheme.error,
                    ),
                    label: Text(
                      order.isPaid ? 'پرداخت شده' : 'پرداخت نشده',
                      style: textTheme.labelMedium?.copyWith(
                        // Use theme colors
                        color: order.isPaid ? theme.colorScheme.tertiary : theme.colorScheme.error,
                        fontWeight: FontWeight.bold
                      ),
                    ),
                    // Use theme colors with opacity
                    backgroundColor: order.isPaid ? theme.colorScheme.tertiaryContainer.withOpacity(0.4) : theme.colorScheme.errorContainer.withOpacity(0.4),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    side: BorderSide.none,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  // Price
                  Text(
                    currencyFormat.format(order.price),
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                      letterSpacing: 0.5, // Slightly increase spacing for currency
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 