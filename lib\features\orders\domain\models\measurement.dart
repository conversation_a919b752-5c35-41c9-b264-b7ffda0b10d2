import 'package:freezed_annotation/freezed_annotation.dart';

part 'measurement.freezed.dart';
part 'measurement.g.dart';

enum SleeveType {
  cuffed,     // کفدار
  cufflinks,  // کفلینکس
  plain,      // ساده
  banded,     // بندکدار
  boat,       // کشتی
  stitchedEdge // لب پخته
}

enum CollarType {
  hindi,      // هندی
  pakistani,  // پاکستانی
  half,       // نیمه
  reverse,    // چپه
  qasemi      // قاسمی
}

enum SkirtType {
  punjabi,
  round,
}

enum TrouserType {
  regular, // معمولی
  wide,    // گشاد
  tight,   // چسپ
}

@freezed
class Measurement with _$Measurement {
  const factory Measurement({
    required double height,
    required double sleeveLength,
    required SleeveType sleeveType,
    required double shoulderWidth,
    required double neckCircumference,
    required CollarType collarType,
    required double sideWidth,
    required double skirtLength,
    required SkirtType skirtType,
    required double trouserLength,
    required TrouserType trouserType,
    required double hemWidth,
    @Default(false) bool hasTrouserPocket,
  }) = _Measurement;

  factory Measurement.fromJson(Map<String, dynamic> json) =>
      _$MeasurementFromJson(json);
} 