import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/firebase_service.dart';
import '../domain/models/customer.dart';

class CustomersRepository {
  final _firestore = FirebaseService.firestore;

  Stream<List<Customer>> watchCustomers(String businessId) {
    return _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('customers')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Customer.fromJson({...doc.data(), 'id': doc.id}))
            .toList());
  }

  Future<void> addCustomer(String businessId, Customer customer) async {
    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('customers')
        .add(customer.toJson()..remove('id'));
  }

  Future<void> updateCustomer(String businessId, Customer customer) async {
    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('customers')
        .doc(customer.id)
        .update(customer.toJson()..remove('id'));
  }

  Future<void> deleteCustomer(String businessId, String customerId) async {
    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('customers')
        .doc(customerId)
        .delete();
  }
} 