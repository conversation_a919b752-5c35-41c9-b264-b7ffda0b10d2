import 'package:cloud_firestore/cloud_firestore.dart' as firestore;
import '../../../core/services/firebase_service.dart';
import '../domain/models/order.dart';
import '../domain/models/measurement.dart';

class OrdersRepository {
  final _firestore = FirebaseService.firestore;

  Stream<List<Order>> watchOrders(String businessId) {
    return _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('orders')
        .orderBy('orderDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Order.fromJson({...doc.data(), 'id': doc.id}))
            .toList());
  }

  Future<void> addOrder(String businessId, Order order) async {
    final orderData = order.toJson()..remove('id');
    if (order.measurement != null) {
      orderData['measurement'] = order.measurement!.toJson();
    }

    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('orders')
        .add(orderData);
  }

  Future<void> updateOrder(String businessId, Order order) async {
    final orderData = order.toJson()..remove('id');
    if (order.measurement != null) {
      orderData['measurement'] = order.measurement!.toJson();
    }

    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('orders')
        .doc(order.id)
        .update(orderData);
  }

  Future<void> deleteOrder(String businessId, String orderId) async {
    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('orders')
        .doc(orderId)
        .delete();
  }

  Future<Order?> getOrder(String businessId, String orderId) async {
    final doc = await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('orders')
        .doc(orderId)
        .get();

    if (!doc.exists) return null;
    return Order.fromJson({...doc.data()!, 'id': doc.id});
  }
} 