import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:shamsi_date/shamsi_date.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tailor_flutter/core/utils/fix_image_gallery_saver.dart';

import '../../domain/models/order.dart';
import '../../domain/models/measurement.dart';
import '../../providers/orders_provider.dart';
import '../../../customers/domain/models/customer.dart';
import '../../../customers/presentation/screens/customer_detail_screen.dart';
import '../../../customers/providers/customers_provider.dart';
import './edit_order_screen.dart';
import './add_order_screen.dart';
import 'package:tailor_flutter/core/widgets/themed_card.dart';
import 'package:tailor_flutter/core/services/image_invoice_service.dart';

class OrderDetailScreen extends ConsumerStatefulWidget {
  final Order order;

  const OrderDetailScreen({super.key, required this.order});

  @override
  ConsumerState<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends ConsumerState<OrderDetailScreen>
    with SingleTickerProviderStateMixin {
  late Order _currentOrder;
  final currencyFormat =
      NumberFormat.currency(locale: 'fa_AF', symbol: '؋', decimalDigits: 0);
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
    _tabController = TabController(
        length: _currentOrder.measurement != null ? 2 : 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _refreshOrderData() async {
    ref.invalidate(ordersProvider);
    ref.invalidate(customersProvider);

    final updatedOrders = await ref.read(ordersProvider.future);
    final updatedOrder = updatedOrders.firstWhere(
        (o) => o.id == _currentOrder.id,
        orElse: () => _currentOrder);
    if (mounted) {
      setState(() {
        _currentOrder = updatedOrder;
        final hasMeasurement = _currentOrder.measurement != null;
        if (_tabController.length != (hasMeasurement ? 2 : 1)) {
          _tabController.dispose();
          _tabController =
              TabController(length: hasMeasurement ? 2 : 1, vsync: this);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final bool hasMeasurement = _currentOrder.measurement != null;

    return Scaffold(
      appBar: AppBar(
        title: Text('سفارش #${_currentOrder.receiptNumber ?? 'N/A'}'),
        elevation: 1,
        shadowColor: theme.shadowColor.withOpacity(0.1),
        surfaceTintColor: Colors.transparent,
        actions: [
          // The Edit button remains visible for quick access.
          IconButton(
            icon: const Icon(Icons.edit_outlined, size: 22),
            tooltip: 'ویرایش سفارش',
            onPressed: () async {
              final result = await Navigator.push<bool>(
                context,
                MaterialPageRoute(
                  builder: (context) => EditOrderScreen(order: _currentOrder),
                ),
              );
              if (result == true && mounted) {
                _refreshOrderData();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('سفارش به‌روزرسانی شد'),
                      behavior: SnackBarBehavior.floating),
                );
              }
            },
          ),
          // Other actions are moved into a PopupMenuButton to save space.
          PopupMenuButton<String>(
            tooltip: "گزینه‌های بیشتر",
            onSelected: (value) {
              if (value == 'print') {
                _showPrintOptions(context);
              } else if (value == 'copy') {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        AddOrderScreen(orderToCopy: _currentOrder),
                  ),
                );
              } else if (value == 'delete') {
                _showDeleteConfirmation(context, ref, _currentOrder);
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: 'print',
                child: ListTile(
                  leading: const Icon(Icons.print_outlined, size: 22),
                  title: const Text('چاپ فاکتور'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              PopupMenuItem<String>(
                value: 'copy',
                child: ListTile(
                  leading: const Icon(Icons.copy_all_outlined, size: 22),
                  title: const Text('کپی کردن سفارش'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuDivider(),
              PopupMenuItem<String>(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete_outline_rounded,
                      size: 22, color: theme.colorScheme.error),
                  title: Text('حذف سفارش',
                      style: TextStyle(color: theme.colorScheme.error)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
          const SizedBox(width: 8),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.tab,
          tabs: [
            Tab(
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                const Icon(Icons.receipt_long_outlined, size: 20),
                const SizedBox(width: 8),
                const Text('مشخصات')
              ]),
            ),
            if (hasMeasurement)
              Tab(
                child:
                    Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  const Icon(Icons.straighten_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text('اندازه‌ها')
                ]),
              ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOrderInfoTab(theme, textTheme),
          if (hasMeasurement) _buildMeasurementTab(theme, textTheme),
        ],
      ),
    );
  }

  Widget _buildOrderInfoTab(ThemeData theme, TextTheme textTheme) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      children: [
        _buildStatusSection(theme, textTheme, _currentOrder),
        const SizedBox(height: 20),
        if (_currentOrder.customer != null)
          _buildCustomerSection(
              context, theme, textTheme, _currentOrder.customer!),
        const SizedBox(height: 20),
        _buildDetailsCard(theme, textTheme, currencyFormat, _currentOrder),
        const SizedBox(height: 20),
        if (_currentOrder.notes != null && _currentOrder.notes!.isNotEmpty)
          _buildNotesSection(theme, textTheme, _currentOrder.notes!),
      ],
    );
  }

  Widget _buildMeasurementTab(ThemeData theme, TextTheme textTheme) {
    if (_currentOrder.measurement == null) return const SizedBox.shrink();
    return ListView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      children: [
        _buildMeasurementCard(theme, textTheme, _currentOrder.measurement!),
      ],
    );
  }

  Widget _buildStatusSection(
      ThemeData theme, TextTheme textTheme, Order order) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (order.status) {
      case OrderStatus.pending:
        statusColor = Colors.orange.shade700;
        statusText = 'در انتظار';
        statusIcon = Icons.pending_actions_rounded;
        break;
      case OrderStatus.inProgress:
        statusColor = theme.colorScheme.primary;
        statusText = 'در حال انجام';
        statusIcon = Icons.construction_rounded;
        break;
      case OrderStatus.completed:
        statusColor = Colors.green.shade700;
        statusText = 'تکمیل شده';
        statusIcon = Icons.check_circle_outline_rounded;
        break;
      case OrderStatus.delivered:
        statusColor = Colors.purple.shade600;
        statusText = 'تحویل داده شده';
        statusIcon = Icons.local_shipping_outlined;
        break;
      case OrderStatus.cancelled:
        statusColor = theme.colorScheme.error;
        statusText = 'لغو شده';
        statusIcon = Icons.cancel_outlined;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 22),
          const SizedBox(width: 12),
          Text(
            statusText,
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
          const Spacer(),
          Chip(
            label: Text(
              order.isPaid ? 'پرداخت شده' : 'پرداخت نشده',
              style: textTheme.labelMedium?.copyWith(
                  color: order.isPaid
                      ? Colors.green.shade800
                      : theme.colorScheme.error,
                  fontWeight: FontWeight.bold),
            ),
            avatar: Icon(
              order.isPaid ? Icons.check_circle_rounded : Icons.cancel_rounded,
              size: 16,
              color: order.isPaid
                  ? Colors.green.shade800
                  : theme.colorScheme.error,
            ),
            backgroundColor: order.isPaid
                ? Colors.green.withOpacity(0.15)
                : theme.colorScheme.error.withOpacity(0.1),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            side: BorderSide.none,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSection(BuildContext context, ThemeData theme,
      TextTheme textTheme, Customer customer) {
    return ThemedCard(
      title: 'مشتری',
      trailing: IconButton(
        icon: const Icon(Icons.open_in_new_rounded, size: 20),
        tooltip: 'مشاهده جزئیات مشتری',
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CustomerDetailScreen(customer: customer)),
          );
        },
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.secondaryContainer,
          child: Icon(Icons.person_pin_rounded,
              color: theme.colorScheme.onSecondaryContainer),
        ),
        title: Text(customer.name,
            style: textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
        subtitle: Text(customer.phoneNumber,
            style: textTheme.bodyMedium
                ?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildDetailsCard(ThemeData theme, TextTheme textTheme,
      NumberFormat currencyFormat, Order order) {
    return ThemedCard(
      title: 'مشخصات سفارش',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(theme, textTheme, Icons.receipt_long_outlined,
              'شماره رسید', order.receiptNumber ?? '-'),
          _buildDetailRow(theme, textTheme, Icons.attach_money_rounded, 'قیمت',
              currencyFormat.format(order.price)),
          _buildDetailRow(theme, textTheme, Icons.calendar_today_outlined,
              'تاریخ سفارش', _formatPersianDate(order.orderDate)),
          _buildDetailRow(
              theme,
              textTheme,
              Icons.event_available_outlined,
              'تاریخ تحویل',
              order.deliveryDate != null
                  ? _formatPersianDate(order.deliveryDate!)
                  : 'تعیین نشده'),
        ],
      ),
    );
  }

  Widget _buildMeasurementCard(
      ThemeData theme, TextTheme textTheme, Measurement m) {
    return ThemedCard(
      title: 'اندازه‌گیری‌ها',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildMeasurementItem(theme, textTheme, Icons.height_rounded, 'قد',
              '${m.height ?? '-'} cm'),
          _buildMeasurementItem(
              theme,
              textTheme,
              Icons.straighten_rounded,
              'آستین',
              '${m.sleeveLength ?? '-'} cm (${_getSleeveTypeText(m.sleeveType ?? SleeveType.plain)})'),
          _buildMeasurementItem(theme, textTheme, Icons.square_foot_rounded,
              'شانه', '${m.shoulderWidth ?? '-'} cm'),
          _buildMeasurementItem(theme, textTheme, Icons.circle_outlined, 'یخن',
              '${m.neckCircumference ?? '-'} cm (${_getCollarTypeText(m.collarType ?? CollarType.pakistani)})'),
          _buildMeasurementItem(theme, textTheme, Icons.width_normal_rounded,
              'بغل', '${m.sideWidth ?? '-'} cm'),
          _buildMeasurementItem(
              theme,
              textTheme,
              Icons.vertical_distribute_rounded,
              'دامن',
              '${m.skirtLength ?? '-'} cm (${_getSkirtTypeText(m.skirtType ?? SkirtType.punjabi)})'),
          _buildMeasurementItem(theme, textTheme, Icons.rule_rounded, 'تنبان',
              '${m.trouserLength ?? '-'} cm (${_getTrouserTypeText(m.trouserType ?? TrouserType.regular)})'),
          _buildMeasurementItem(theme, textTheme, Icons.expand_rounded, 'پاچه',
              '${m.hemWidth ?? '-'} cm'),
          _buildMeasurementItem(
              theme,
              textTheme,
              Icons.check_box_outline_blank_rounded,
              'جیب تنبان',
              m.hasTrouserPocket == true ? 'دارد' : 'ندارد'),
        ].expand((widget) => [widget, const SizedBox(height: 10)]).toList()
          ..removeLast(),
      ),
    );
  }

  Widget _buildNotesSection(
      ThemeData theme, TextTheme textTheme, String notes) {
    return ThemedCard(
      title: 'یادداشت‌ها',
      child: Text(notes, style: textTheme.bodyMedium?.copyWith(height: 1.5)),
    );
  }

  Widget _buildDetailRow(ThemeData theme, TextTheme textTheme, IconData icon,
      String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: theme.colorScheme.primary),
          const SizedBox(width: 12),
          Text('$label:',
              style:
                  textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500)),
          const Spacer(),
          Text(value,
              style: textTheme.bodyMedium
                  ?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
        ],
      ),
    );
  }

  Widget _buildMeasurementItem(ThemeData theme, TextTheme textTheme,
      IconData icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: theme.dividerColor.withOpacity(0.5)),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.primary),
          const SizedBox(width: 12),
          Text(
            '$label:',
            style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface),
          ),
          const Spacer(),
          Flexible(
            child: Text(
              value,
              style: textTheme.bodyMedium
                  ?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  String _getSleeveTypeText(SleeveType type) {
    switch (type) {
      case SleeveType.cuffed:
        return 'کفدار';
      case SleeveType.cufflinks:
        return 'کفلینکس';
      case SleeveType.plain:
        return 'ساده';
      case SleeveType.banded:
        return 'بندکدار';
      case SleeveType.boat:
        return 'کشتی';
      case SleeveType.stitchedEdge:
        return 'لب پخته';
    }
  }

  String _getCollarTypeText(CollarType type) {
    switch (type) {
      case CollarType.hindi:
        return 'هندی';
      case CollarType.pakistani:
        return 'پاکستانی';
      case CollarType.half:
        return 'نیمه';
      case CollarType.reverse:
        return 'چپه';
      case CollarType.qasemi:
        return 'قاسمی';
    }
  }

  String _getSkirtTypeText(SkirtType type) {
    switch (type) {
      case SkirtType.punjabi:
        return 'پنجابی';
      case SkirtType.round:
        return 'گرد';
    }
  }

  String _getTrouserTypeText(TrouserType type) {
    switch (type) {
      case TrouserType.regular:
        return 'معمولی';
      case TrouserType.wide:
        return 'گشاد';
      case TrouserType.tight:
        return 'چسپ';
    }
  }

  String _formatPersianDate(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    final formatter = jalali.formatter;
    return '${formatter.yyyy}/${formatter.mm}/${formatter.dd}';
  }

  Future<void> _showPrintOptions(BuildContext context) async {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Directionality(
        textDirection: ui.TextDirection.rtl,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'فاکتور سفارش',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.primary.withAlpha(25),
                  child: Icon(Icons.preview, color: theme.colorScheme.primary),
                ),
                title: const Text('مشاهده فاکتور'),
                subtitle: const Text('نمایش فاکتور سفارش'),
                onTap: () {
                  Navigator.pop(context);
                  _previewInvoice();
                },
              ),
              const Divider(),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.secondary.withAlpha(25),
                  child: Icon(Icons.share, color: theme.colorScheme.secondary),
                ),
                title: const Text('اشتراک‌گذاری'),
                subtitle: const Text('ارسال تصویر فاکتور'),
                onTap: () {
                  Navigator.pop(context);
                  _shareInvoice();
                },
              ),
              const Divider(),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.tertiary.withAlpha(25),
                  child:
                      Icon(Icons.save_alt, color: theme.colorScheme.tertiary),
                ),
                title: const Text('ذخیره در گالری'),
                subtitle: const Text('ذخیره تصویر فاکتور در دستگاه'),
                onTap: () {
                  Navigator.pop(context);
                  _saveInvoice();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _previewInvoice() async {
    try {
      // نمایش دیالوگ بارگذاری
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تولید تصویر فاکتور
      final imageBytes = await ImageInvoiceService.generateOrderImage(
        _currentOrder,
        context,
      );

      // بستن دیالوگ بارگذاری
      if (mounted) Navigator.pop(context);

      // نمایش تصویر در دیالوگ
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('پیش‌نمایش فاکتور'),
                  centerTitle: true,
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Image.memory(imageBytes),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.share),
                        label: const Text('اشتراک‌گذاری'),
                        onPressed: () {
                          Navigator.pop(context);
                          _shareInvoice();
                        },
                      ),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.save_alt),
                        label: const Text('ذخیره'),
                        onPressed: () {
                          Navigator.pop(context);
                          _saveInvoice();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }
    } catch (e) {
      // بستن دیالوگ بارگذاری در صورت خطا
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در نمایش فاکتور: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _shareInvoice() async {
    try {
      // نمایش دیالوگ بارگذاری
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // تولید تصویر فاکتور
      final BuildContext captureContext = context;
      final imageBytes = await ImageInvoiceService.generateOrderImage(
        _currentOrder,
        captureContext,
      );

      // ذخیره موقت تصویر
      final tempDir = await getTemporaryDirectory();
      final fileName =
          'سفارش_${_currentOrder.receiptNumber ?? _currentOrder.id}';
      final file = await File('${tempDir.path}/$fileName.png').create();
      await file.writeAsBytes(imageBytes);

      // بستن دیالوگ بارگذاری
      if (mounted) Navigator.pop(context);

      // اشتراک‌گذاری تصویر
      if (mounted) {
        final xFile = XFile(file.path, mimeType: 'image/png');
        final params = ShareParams(
          files: [xFile],
          text:
              'فاکتور سفارش ${_currentOrder.receiptNumber ?? _currentOrder.id}',
        );

        await SharePlus.instance.share(params);
      }
    } catch (e) {
      // بستن دیالوگ بارگذاری در صورت خطا
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در اشتراک‌گذاری فاکتور: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _saveInvoice() async {
    try {
      // بررسی و درخواست دسترسی‌ها
      PermissionStatus status;

      // برای اندروید 13 و بالاتر
      if (await Permission.photos.request().isGranted) {
        status = PermissionStatus.granted;
      }
      // برای اندروید 12 و پایین‌تر
      else if (await Permission.storage.request().isGranted) {
        status = PermissionStatus.granted;
      } else {
        status = PermissionStatus.denied;
      }

      // اگر دسترسی داده نشد
      if (status.isDenied) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'برای ذخیره فاکتور در گالری، نیاز به دسترسی به حافظه دستگاه است'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 4),
            ),
          );
        }
        return;
      }

      // نمایش دیالوگ بارگذاری
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // تولید تصویر فاکتور
      final BuildContext captureContext = context;
      final imageBytes = await ImageInvoiceService.generateOrderImage(
        _currentOrder,
        captureContext,
      );

      // بستن دیالوگ بارگذاری
      if (mounted) Navigator.pop(context);

      // ذخیره تصویر در گالری
      final result = await ImageSaver.saveImage(
        imageBytes,
        quality: 100,
        name:
            'دوخت‌یار_سفارش_${_currentOrder.receiptNumber ?? _currentOrder.id}',
      );

      if (mounted) {
        if (result['isSuccess'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فاکتور با موفقیت در گالری ذخیره شد'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'خطا در ذخیره فاکتور: ${result['errorMessage'] ?? 'خطای نامشخص'}'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      // بستن دیالوگ بارگذاری در صورت خطا
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در ذخیره فاکتور: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _showDeleteConfirmation(
      BuildContext context, WidgetRef ref, Order order) async {
    final theme = Theme.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('حذف سفارش'),
        content: Text(
            'آیا از حذف سفارش ${order.receiptNumber}# اطمینان دارید؟ این عمل قابل بازگشت نیست.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('انصراف'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
            ),
            child: const Text('حذف نهایی'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(ordersNotifierProvider.notifier).deleteOrder(order.id);
        if (mounted) {
          ref.invalidate(ordersProvider);
          ref.invalidate(customersProvider);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('سفارش با موفقیت حذف شد'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطا در حذف سفارش: ${e.toString()}'),
              backgroundColor: theme.colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }
}
