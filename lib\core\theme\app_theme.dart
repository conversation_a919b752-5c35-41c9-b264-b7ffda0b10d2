import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'colors.dart';
import 'constants.dart';
import 'text_styles.dart';

class AppTheme {
  // Factory method to create the light theme
  static ThemeData createLightTheme() {
    final baseTheme =
        ThemeData(useMaterial3: true, brightness: Brightness.light);
    final colorScheme = ColorScheme(
      brightness: Brightness.light,
      primary: AppColors.primary, // Keep existing primary
      onPrimary: Colors.white, // Text on primary
      primaryContainer: AppColors.primaryContainer, // Lighter shade of primary
      onPrimaryContainer: AppColors.primary, // Text on primary container
      secondary: AppColors.secondary, // Keep existing secondary
      onSecondary: Colors.white, // Text on secondary
      secondaryContainer:
          AppColors.secondaryContainer, // Lighter shade of secondary
      onSecondaryContainer: AppColors.secondary, // Text on secondary container
      tertiary: AppColors.success, // Use success color as tertiary
      onTertiary: Colors.white, // Text on tertiary
      tertiaryContainer:
          AppColors.successContainer, // Lighter shade of tertiary
      onTertiaryContainer: AppColors.success, // Text on tertiary container
      error: AppColors.error, // Keep existing error
      onError: Colors.white, // Text on error
      errorContainer: AppColors.errorContainer, // Lighter shade of error
      onErrorContainer: AppColors.error, // Text on error container
      background: AppColors.background, // Keep existing background
      onBackground: AppColors.textPrimary, // Main text color
      surface: AppColors.surface, // Card/dialog backgrounds
      onSurface: AppColors.textPrimary, // Text on surface
      surfaceVariant: AppColors
          .secondaryContainer, // Use secondary container as surface variant
      onSurfaceVariant: AppColors.textSecondary, // Secondary text color
      outline: AppColors.border, // Borders, dividers
      outlineVariant: AppColors.border.withOpacity(0.5), // Lighter borders
      shadow: Colors.black.withOpacity(0.1), // Standard shadow
      scrim: Colors.black.withOpacity(0.2), // Standard scrim
      inverseSurface:
          AppColors.textPrimary.withOpacity(0.9), // For tooltips, snackbars
      onInverseSurface: AppColors.background, // Text on inverse surface
      inversePrimary:
          AppColors.primary.withOpacity(0.8), // Inverse primary interaction
      surfaceTint:
          AppColors.primary.withOpacity(0.05), // Material 3 surface tint
    );

    return baseTheme.copyWith(
      colorScheme: colorScheme,
      scaffoldBackgroundColor: colorScheme.background,

      // Text Theme
      textTheme: GoogleFonts.vazirmatnTextTheme(
        baseTheme.textTheme.copyWith(
          displayLarge: AppTextStyles.displayLarge
              .copyWith(color: colorScheme.onBackground),
          displayMedium: AppTextStyles.displayMedium
              .copyWith(color: colorScheme.onBackground),
          displaySmall: AppTextStyles.displaySmall
              .copyWith(color: colorScheme.onBackground),
          headlineLarge: AppTextStyles.headlineLarge
              .copyWith(color: colorScheme.onBackground),
          headlineMedium: AppTextStyles.headlineMedium
              .copyWith(color: colorScheme.onBackground),
          headlineSmall: AppTextStyles.headlineSmall
              .copyWith(color: colorScheme.onBackground),
          titleLarge: AppTextStyles.titleLarge
              .copyWith(color: colorScheme.onBackground),
          titleMedium: AppTextStyles.titleMedium
              .copyWith(color: colorScheme.onBackground),
          titleSmall: AppTextStyles.titleSmall
              .copyWith(color: colorScheme.onBackground),
          bodyLarge:
              AppTextStyles.bodyLarge.copyWith(color: colorScheme.onSurface),
          bodyMedium:
              AppTextStyles.bodyMedium.copyWith(color: colorScheme.onSurface),
          bodySmall: AppTextStyles.bodySmall
              .copyWith(color: colorScheme.onSurfaceVariant),
          labelLarge: AppTextStyles.labelLarge
              .copyWith(color: colorScheme.onSurfaceVariant),
          labelMedium: AppTextStyles.labelMedium
              .copyWith(color: colorScheme.onSurfaceVariant),
          labelSmall: AppTextStyles.labelSmall
              .copyWith(color: colorScheme.onSurfaceVariant),
        ),
      ),

      // AppBar Theme
      appBarTheme: AppBarTheme(
        centerTitle: true,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: 4,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: colorScheme.surfaceTint,
        titleTextStyle:
            AppTextStyles.titleLarge.copyWith(color: colorScheme.onSurface),
        iconTheme: IconThemeData(color: colorScheme.primary),
        actionsIconTheme: IconThemeData(color: colorScheme.primary),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 1, // Reduced elevation for flatter look
        shadowColor: colorScheme.shadow.withOpacity(0.05),
        shape: RoundedRectangleBorder(
          borderRadius: AppRadius.cardRadius,
          side: BorderSide(color: colorScheme.outlineVariant, width: 1),
        ),
        margin: EdgeInsets.zero,
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: AppSpacing.buttonPadding,
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(borderRadius: AppRadius.buttonRadius),
          minimumSize: const Size(120, 45),
          textStyle: AppTextStyles.titleMedium.copyWith(
              color: colorScheme.onPrimary, fontWeight: FontWeight.bold),
        ),
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          elevation: 1,
          padding: AppSpacing.buttonPadding,
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(borderRadius: AppRadius.buttonRadius),
          minimumSize: const Size(120, 45),
          textStyle: AppTextStyles.titleMedium.copyWith(
              color: colorScheme.onPrimary, fontWeight: FontWeight.bold),
        ),
      ),
      // Corrected parameter name
      buttonTheme: baseTheme.buttonTheme.copyWith(
          // Use tonal button theme settings here if needed
          // For example: colorScheme: colorScheme.copyWith(primary: colorScheme.secondaryContainer, onPrimary: colorScheme.onSecondaryContainer)
          ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: AppSpacing.buttonPadding,
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.primary, width: 1.5),
          shape: RoundedRectangleBorder(borderRadius: AppRadius.buttonRadius),
          minimumSize: const Size(120, 45),
          textStyle: AppTextStyles.titleMedium.copyWith(
              color: colorScheme.primary, fontWeight: FontWeight.bold),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: AppSpacing.buttonPadding,
          foregroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(borderRadius: AppRadius.buttonRadius),
          textStyle:
              AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.bold),
        ),
      ),

      // FAB Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 4,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.circular)),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceVariant.withOpacity(0.4),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
          borderSide: BorderSide(color: colorScheme.outlineVariant),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
          borderSide: BorderSide(color: colorScheme.outlineVariant),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
          borderSide: BorderSide(color: colorScheme.error, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.md),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        labelStyle: AppTextStyles.bodyMedium
            .copyWith(color: colorScheme.onSurfaceVariant),
        hintStyle: AppTextStyles.bodyMedium
            .copyWith(color: colorScheme.onSurfaceVariant.withOpacity(0.7)),
        errorStyle: AppTextStyles.bodySmall.copyWith(color: colorScheme.error),
        prefixIconColor: colorScheme.onSurfaceVariant,
        suffixIconColor: colorScheme.onSurfaceVariant,
      ),

      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected))
            return colorScheme.primary;
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(colorScheme.onPrimary),
        side: BorderSide(color: colorScheme.outline, width: 1.5),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.sm)),
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected))
            return colorScheme.primary;
          return colorScheme.outline;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected))
            return colorScheme.primary.withOpacity(0.5);
          return colorScheme.surfaceVariant;
        }),
        trackOutlineColor: MaterialStateProperty.all(Colors.transparent),
      ),

      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected))
            return colorScheme.primary;
          return colorScheme.outline;
        }),
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: colorScheme.primary,
        inactiveTrackColor: colorScheme.primary.withOpacity(0.3),
        thumbColor: colorScheme.primary,
        overlayColor: colorScheme.primary.withOpacity(0.2),
        trackHeight: 4,
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.surface,
        elevation: 6,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.lg)),
        titleTextStyle:
            AppTextStyles.headlineSmall.copyWith(color: colorScheme.onSurface),
        contentTextStyle: AppTextStyles.bodyMedium
            .copyWith(color: colorScheme.onSurfaceVariant),
      ),

      // Bottom Sheet Theme
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: colorScheme.surface,
        elevation: 8,
        shape:
            RoundedRectangleBorder(borderRadius: AppRadius.bottomSheetRadius),
        modalBackgroundColor: colorScheme.surface,
        modalElevation: 8,
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outlineVariant,
        thickness: 1,
        space: 1,
      ),

      // TabBar Theme
      tabBarTheme: TabBarThemeData(
        indicator: BoxDecoration(
          border:
              Border(bottom: BorderSide(color: colorScheme.primary, width: 3)),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        labelStyle:
            AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: AppTextStyles.titleSmall,
        overlayColor:
            MaterialStateProperty.all(colorScheme.primary.withOpacity(0.1)),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.secondaryContainer.withOpacity(0.6),
        disabledColor: colorScheme.onSurface.withOpacity(0.12),
        selectedColor: colorScheme.primary,
        secondarySelectedColor: colorScheme.secondary,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        labelStyle: AppTextStyles.bodySmall
            .copyWith(color: colorScheme.onSecondaryContainer),
        secondaryLabelStyle: AppTextStyles.bodySmall
            .copyWith(color: colorScheme.onSecondaryContainer),
        brightness: Brightness.light,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.xl)),
        side: BorderSide.none, // Keep no border for default state
        checkmarkColor: colorScheme.onPrimary,
        iconTheme:
            IconThemeData(color: colorScheme.onSecondaryContainer, size: 18),
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        iconColor: colorScheme.primary,
        titleTextStyle:
            AppTextStyles.bodyLarge.copyWith(color: colorScheme.onSurface),
        subtitleTextStyle: AppTextStyles.bodyMedium
            .copyWith(color: colorScheme.onSurfaceVariant),
        dense: false,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.md)),
      ),

      // Tooltip Theme
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: colorScheme.inverseSurface, // Use inverseSurface directly
          borderRadius: BorderRadius.circular(AppRadius.sm),
        ),
        textStyle: AppTextStyles.bodySmall
            .copyWith(color: colorScheme.onInverseSurface),
        preferBelow: false,
        waitDuration: const Duration(milliseconds: 300),
      ),

      // Scrollbar Theme
      scrollbarTheme: ScrollbarThemeData(
        thumbColor:
            MaterialStateProperty.all(colorScheme.primary.withOpacity(0.6)),
        radius: const Radius.circular(4),
        thickness: MaterialStateProperty.all(6),
        interactive: true,
      ),

      // Other customizations
      splashColor: colorScheme.primary.withOpacity(0.1),
      highlightColor: colorScheme.primary.withOpacity(0.05),
      focusColor: colorScheme.primary.withOpacity(0.15),
    );
  }
}
