import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../features/auth/providers/auth_provider.dart';
import '../data/customers_repository.dart';
import '../domain/models/customer.dart';

part 'customers_provider.g.dart';

@riverpod
CustomersRepository customersRepository(CustomersRepositoryRef ref) {
  return CustomersRepository();
}

@riverpod
Stream<List<Customer>> customers(CustomersRef ref) async* {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    yield [];
    return;
  }

  yield* ref.watch(customersRepositoryProvider).watchCustomers(profile.id);
}

@riverpod
class CustomersNotifier extends _$CustomersNotifier {
  @override
  FutureOr<void> build() async {}

  Future<void> addCustomer({
    required String name,
    required String phoneNumber,
    String? address,
    String? notes,
  }) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    final customer = Customer(
      id: '',
      name: name,
      phoneNumber: phoneNumber,
      address: address,
      notes: notes,
    );

    await ref
        .read(customersRepositoryProvider)
        .addCustomer(profile.id, customer);
  }

  Future<void> updateCustomer(Customer customer) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(customersRepositoryProvider)
        .updateCustomer(profile.id, customer);
  }

  Future<void> deleteCustomer(String customerId) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(customersRepositoryProvider)
        .deleteCustomer(profile.id, customerId);
  }
} 