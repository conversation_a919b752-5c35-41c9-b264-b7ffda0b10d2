import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart'
    hide TextDirection; // Hide TextDirection from intl
import 'package:shamsi_date/shamsi_date.dart';
import 'package:persian_datetime_picker/persian_datetime_picker.dart';
import '../../domain/models/order.dart';
import '../../domain/models/measurement.dart';
import '../../providers/orders_provider.dart';
import '../../../customers/domain/models/customer.dart'; // Needed for customer display
import '../../../customers/presentation/screens/customer_detail_screen.dart'; // Needed for customer navigation
import 'package:tailor_flutter/core/widgets/themed_card.dart'; // Import the themed card
import '../../../customers/providers/customers_provider.dart'; // Add this import

class EditOrderScreen extends ConsumerStatefulWidget {
  final Order order;

  const EditOrderScreen({super.key, required this.order});

  @override
  ConsumerState<EditOrderScreen> createState() => _EditOrderScreenState();
}

class _EditOrderScreenState extends ConsumerState<EditOrderScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _receiptNumberController;
  late final TextEditingController _priceController;
  late final TextEditingController _notesController;
  late DateTime _orderDate;
  late DateTime? _deliveryDate;
  late OrderStatus _status;
  late bool _isPaid;
  bool _isLoading = false;
  late TabController _tabController;
  Customer? _customer;

  // Measurement fields
  final _heightController = TextEditingController();
  final _sleeveLengthController = TextEditingController();
  SleeveType? _selectedSleeveType;
  final _shoulderWidthController = TextEditingController();
  final _neckCircumferenceController = TextEditingController();
  CollarType? _selectedCollarType;
  final _sideWidthController = TextEditingController();
  final _skirtLengthController = TextEditingController();
  SkirtType? _selectedSkirtType;
  final _trouserLengthController = TextEditingController();
  TrouserType? _selectedTrouserType;
  final _hemWidthController = TextEditingController();
  bool _hasTrouserPocket = false;

  @override
  void initState() {
    super.initState();
    final hasMeasurement = widget.order.measurement != null;
    _tabController = TabController(length: hasMeasurement ? 2 : 1, vsync: this);
    _customer = widget.order.customer;

    // Initialize controllers with existing order data
    _receiptNumberController =
        TextEditingController(text: widget.order.receiptNumber);
    _priceController =
        TextEditingController(text: widget.order.price.toString());
    _notesController = TextEditingController(text: widget.order.notes ?? '');
    _orderDate = widget.order.orderDate;
    _deliveryDate = widget.order.deliveryDate;
    _status = widget.order.status;
    _isPaid = widget.order.isPaid;

    // Initialize measurement controllers if measurement exists
    if (hasMeasurement) {
      final m = widget.order.measurement!;
      _heightController.text = m.height?.toString() ?? '';
      _sleeveLengthController.text = m.sleeveLength?.toString() ?? '';
      _selectedSleeveType = m.sleeveType;
      _shoulderWidthController.text = m.shoulderWidth?.toString() ?? '';
      _neckCircumferenceController.text = m.neckCircumference?.toString() ?? '';
      _selectedCollarType = m.collarType;
      _sideWidthController.text = m.sideWidth?.toString() ?? '';
      _skirtLengthController.text = m.skirtLength?.toString() ?? '';
      _selectedSkirtType = m.skirtType;
      _trouserLengthController.text = m.trouserLength?.toString() ?? '';
      _selectedTrouserType = m.trouserType;
      _hemWidthController.text = m.hemWidth?.toString() ?? '';
      _hasTrouserPocket = m.hasTrouserPocket ?? false;
    } else {
      // Provide default values if no measurement exists (though the tab won't show)
      _selectedSleeveType = SleeveType.plain;
      _selectedCollarType = CollarType.pakistani;
      _selectedSkirtType = SkirtType.punjabi;
      _selectedTrouserType = TrouserType.regular;
    }
  }

  // --- Update Logic ---
  Future<void> _updateOrder() async {
    // Basic validation first
    if (_deliveryDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('لطفا تاریخ تحویل را انتخاب کنید'),
            behavior: SnackBarBehavior.floating),
      );
      _tabController.animateTo(0); // Switch to the tab with the delivery date
      return;
    }

    // Validate the form
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('لطفا خطاهای فرم را در هر دو تب بررسی کنید'),
            behavior: SnackBarBehavior.floating),
      );
      if (_tabController.index != 0) _tabController.animateTo(0);
      return;
    }

    setState(() => _isLoading = true);
    try {
      // Create Measurement object from controllers, providing defaults
      final updatedMeasurement = widget.order.measurement != null
          ? Measurement(
              height: double.tryParse(_heightController.text.trim()) ?? 0.0,
              sleeveLength:
                  double.tryParse(_sleeveLengthController.text.trim()) ?? 0.0,
              sleeveType: _selectedSleeveType ?? SleeveType.plain,
              shoulderWidth:
                  double.tryParse(_shoulderWidthController.text.trim()) ?? 0.0,
              neckCircumference:
                  double.tryParse(_neckCircumferenceController.text.trim()) ??
                      0.0,
              collarType: _selectedCollarType ?? CollarType.pakistani,
              sideWidth:
                  double.tryParse(_sideWidthController.text.trim()) ?? 0.0,
              skirtLength:
                  double.tryParse(_skirtLengthController.text.trim()) ?? 0.0,
              skirtType: _selectedSkirtType ?? SkirtType.punjabi,
              trouserLength:
                  double.tryParse(_trouserLengthController.text.trim()) ?? 0.0,
              trouserType: _selectedTrouserType ?? TrouserType.regular,
              hemWidth: double.tryParse(_hemWidthController.text.trim()) ?? 0.0,
              hasTrouserPocket: _hasTrouserPocket,
            )
          : null;

      // Create updated order object without customer field
      final updatedOrder = Order(
        id: widget.order.id,
        customerId: widget.order.customerId,
        receiptNumber: _receiptNumberController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        orderDate: _orderDate,
        deliveryDate: _deliveryDate,
        status: _status,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
        isPaid: _isPaid,
        measurement: updatedMeasurement,
      );

      await ref.read(ordersNotifierProvider.notifier).updateOrder(updatedOrder);

      if (mounted) {
        // Invalidate both providers to ensure UI updates
        ref.invalidate(ordersProvider);
        ref.invalidate(customersProvider);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('سفارش با موفقیت ویرایش شد'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
        // Navigate back to the orders list screen
        Navigator.of(context).popUntil((route) => route
            .isFirst); // Pop until the first route (usually dashboard/orders)
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطا در ویرایش سفارش: ${e.toString()}'),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  // --- Helper Methods (Date Formatting, etc.) ---
  String _formatPersianDisplayDate(DateTime? date) {
    if (date == null) return 'انتخاب نشده';
    final jalali = Jalali.fromDateTime(date);
    final formatter = jalali.formatter;
    return '${formatter.wN} ${formatter.d} ${formatter.mN} ${formatter.yyyy}';
  }

  Future<void> _selectDate(BuildContext context, bool isOrderDate) async {
    final Jalali? picked = await showPersianDatePicker(
      context: context,
      initialDate: Jalali.fromDateTime(
          isOrderDate ? _orderDate : (_deliveryDate ?? DateTime.now())),
      firstDate: Jalali(1380),
      lastDate: Jalali(1450),
    );

    if (picked != null) {
      setState(() {
        final pickedDateTime = picked.toDateTime();
        if (isOrderDate) {
          _orderDate = pickedDateTime;
        } else {
          _deliveryDate = pickedDateTime;
        }
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _receiptNumberController.dispose();
    _priceController.dispose();
    _notesController.dispose();
    _heightController.dispose();
    _sleeveLengthController.dispose();
    _shoulderWidthController.dispose();
    _neckCircumferenceController.dispose();
    _sideWidthController.dispose();
    _skirtLengthController.dispose();
    _trouserLengthController.dispose();
    _hemWidthController.dispose();
    super.dispose();
  }

  // --- Build Method ---
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final bool hasMeasurement = widget.order.measurement != null;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text('ویرایش سفارش #${widget.order.receiptNumber ?? 'N/A'}'),
          centerTitle: true,
          elevation: 1,
          shadowColor: theme.shadowColor.withOpacity(0.1),
          surfaceTintColor: Colors.transparent,
          bottom: TabBar(
            controller: _tabController,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: [
              const Tab(
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.receipt_long_outlined, size: 20),
                      SizedBox(width: 8),
                      Text('مشخصات')
                    ]),
              ),
              if (hasMeasurement)
                const Tab(
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.straighten_outlined, size: 20),
                        SizedBox(width: 8),
                        Text('اندازه‌ها')
                      ]),
                ),
            ],
          ),
        ),
        body: Form(
          key: _formKey, // Single form key for simplicity
          child: Column(
            children: [
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOrderDetailsTab(
                        theme, textTheme), // Order Details Tab
                    if (hasMeasurement)
                      _buildMeasurementsTab(
                          theme, textTheme), // Measurements Tab
                  ],
                ),
              ),
              _buildSubmitButtonArea(context, theme), // Submit button area
            ],
          ),
        ),
      ),
    );
  }

  // --- UI Helper Widgets ---

  // Common InputDecoration (Can be reused from AddOrderScreen or defined here)
  InputDecoration _inputDecoration(
      ThemeData theme, String label, IconData? icon,
      {String? suffixText}) {
    final border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide(color: theme.dividerColor),
    );
    return InputDecoration(
      labelText: label,
      prefixIcon: icon != null ? Icon(icon, size: 20) : null,
      suffixText: suffixText,
      border: border,
      enabledBorder: border,
      focusedBorder: border.copyWith(
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2)),
      errorBorder: border.copyWith(
          borderSide: BorderSide(color: theme.colorScheme.error, width: 1)),
      focusedErrorBorder: border.copyWith(
          borderSide: BorderSide(color: theme.colorScheme.error, width: 2)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    );
  }

  // Submit Button Area (Similar to AddOrderScreen)
  Widget _buildSubmitButtonArea(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.fromLTRB(
          16, 12, 16, 16 + MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: FilledButton.icon(
        onPressed: _isLoading ? null : _updateOrder,
        icon: _isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: theme.colorScheme.onPrimary))
            : const Icon(Icons.save_alt_rounded), // Use save_alt icon
        label: Text(_isLoading ? 'در حال ذخیره...' : 'ذخیره تغییرات'),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 14),
          textStyle: theme.textTheme.titleMedium
              ?.copyWith(fontWeight: FontWeight.bold),
          minimumSize: const Size(double.infinity, 50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
    );
  }

  // --- Tab Content Builders ---

  Widget _buildOrderDetailsTab(ThemeData theme, TextTheme textTheme) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(
          16, 16, 16, 24), // Padding for scrollbar and button area
      children: [
        _buildCustomerInfo(theme, textTheme), // Show customer info (read-only)
        const SizedBox(height: 24),
        _buildOrderDetailsSection(theme, textTheme), // Editable order details
      ],
    );
  }

  Widget _buildMeasurementsTab(ThemeData theme, TextTheme textTheme) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      children: [
        _buildMeasurementSection(theme, textTheme), // Editable measurements
      ],
    );
  }

  // --- Section Builders ---

  Widget _buildCustomerInfo(ThemeData theme, TextTheme textTheme) {
    // Use ThemedCard for consistent styling
    return ThemedCard(
      title: 'مشتری',
      trailing: IconButton(
        icon: const Icon(Icons.open_in_new_rounded, size: 20),
        tooltip: 'مشاهده جزئیات مشتری',
        onPressed: _customer == null
            ? null
            : () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) =>
                          CustomerDetailScreen(customer: _customer!)),
                );
              },
      ),
      child: _customer == null
          ? const ListTile(
              title: Text('مشتری یافت نشد'), leading: Icon(Icons.error_outline))
          : ListTile(
              leading: CircleAvatar(
                backgroundColor: theme.colorScheme.secondaryContainer,
                child: Icon(Icons.person_pin_rounded,
                    color: theme.colorScheme.onSecondaryContainer),
              ),
              title: Text(_customer!.name,
                  style: textTheme.bodyLarge
                      ?.copyWith(fontWeight: FontWeight.bold)),
              subtitle: Text(_customer!.phoneNumber),
              contentPadding: EdgeInsets.zero,
            ),
    );
  }

  Widget _buildOrderDetailsSection(ThemeData theme, TextTheme textTheme) {
    final readOnlyDecoration = _inputDecoration(theme, '', null).copyWith(
      fillColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
      filled: true,
    );
    final readOnlyTextStyle = textTheme.bodyLarge?.copyWith(
      color: theme.colorScheme.onPrimaryContainer,
    );

    return ThemedCard(
      title: 'ویرایش اطلاعات سفارش',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Receipt Number (Read-only)
          InputDecorator(
            decoration: readOnlyDecoration.copyWith(
                labelText: 'شماره رسید',
                prefixIcon: const Icon(Icons.receipt_long_outlined)),
            child: Text(
              _receiptNumberController.text,
              style: readOnlyTextStyle,
            ),
          ),
          const SizedBox(height: 16),
          // Price
          TextFormField(
            controller: _priceController,
            decoration:
                _inputDecoration(theme, 'قیمت', Icons.attach_money_rounded),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value == null || value.isEmpty)
                return 'لطفا قیمت را وارد کنید';
              if (double.tryParse(value) == null)
                return 'لطفا عدد معتبر وارد کنید';
              return null;
            },
          ),
          const SizedBox(height: 16),
          // Order Date (Read-only)
          // GestureDetector removed, onTap removed from InputDecorator
          InputDecorator(
            decoration: readOnlyDecoration.copyWith(
                labelText: 'تاریخ سفارش',
                prefixIcon: const Icon(Icons.calendar_today_outlined)),
            child: Text(
              _formatPersianDisplayDate(_orderDate),
              style: readOnlyTextStyle,
            ),
          ),
          const SizedBox(height: 16),
          // Delivery Date
          GestureDetector(
            onTap: () => _selectDate(context, false),
            child: InputDecorator(
              decoration: _inputDecoration(
                  theme, 'تاریخ تحویل', Icons.local_shipping_outlined),
              child: Text(_formatPersianDisplayDate(_deliveryDate),
                  style: textTheme.bodyLarge),
            ),
          ),
          const SizedBox(height: 16),
          // Status Dropdown
          DropdownButtonFormField<OrderStatus>(
            value: _status,
            decoration:
                _inputDecoration(theme, 'وضعیت سفارش', Icons.list_alt_outlined),
            items: OrderStatus.values
                .map((status) => DropdownMenuItem(
                    value: status, child: Text(_getStatusText(status))))
                .toList(),
            onChanged: (value) => setState(() => _status = value!),
          ),
          const SizedBox(height: 16),
          // Paid Status Switch
          SwitchListTile(
            title: const Text('وضعیت پرداخت'),
            value: _isPaid,
            onChanged: (value) => setState(() => _isPaid = value),
            secondary: Icon(
                _isPaid
                    ? Icons.check_circle_outline_rounded
                    : Icons.highlight_off_rounded,
                color: _isPaid
                    ? Colors.green
                    : Theme.of(context).colorScheme.onSurfaceVariant),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                    color: Theme.of(context).dividerColor.withOpacity(0.5))),
            tileColor:
                Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
          ),
          const SizedBox(height: 16),
          // Notes TextField
          TextFormField(
            controller: _notesController,
            decoration: _inputDecoration(
                theme, 'یادداشت‌ها (اختیاری)', Icons.note_alt_outlined),
            maxLines: 3,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementSection(ThemeData theme, TextTheme textTheme) {
    // Reusing the structure from AddOrderScreen for consistency
    return ThemedCard(
      title: 'ویرایش اندازه‌گیری‌ها',
      child: Column(
        children: [
          Row(children: [
            Expanded(
                child: _buildMeasurementTextField(theme, _heightController,
                    'قد', Icons.height_rounded, 'cm')),
            const SizedBox(width: 12),
            Expanded(
                child: _buildMeasurementTextField(
                    theme,
                    _shoulderWidthController,
                    'شانه',
                    Icons.square_foot_rounded,
                    'cm')),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                flex: 3,
                child: _buildMeasurementTextField(
                    theme,
                    _sleeveLengthController,
                    'آستین',
                    Icons.straighten_rounded,
                    'cm')),
            const SizedBox(width: 12),
            Expanded(
                flex: 2,
                child: _buildMeasurementDropdown<SleeveType>(
                    theme,
                    'نوع',
                    _selectedSleeveType,
                    SleeveType.values,
                    _getSleeveTypeText,
                    (v) => setState(() => _selectedSleeveType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                flex: 3,
                child: _buildMeasurementTextField(
                    theme,
                    _neckCircumferenceController,
                    'یخن',
                    Icons.circle_outlined,
                    'cm')),
            const SizedBox(width: 12),
            Expanded(
                flex: 2,
                child: _buildMeasurementDropdown<CollarType>(
                    theme,
                    'نوع',
                    _selectedCollarType,
                    CollarType.values,
                    _getCollarTypeText,
                    (v) => setState(() => _selectedCollarType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                child: _buildMeasurementTextField(theme, _sideWidthController,
                    'بغل', Icons.width_normal_rounded, 'cm')),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                flex: 3,
                child: _buildMeasurementTextField(theme, _skirtLengthController,
                    'دامن', Icons.vertical_distribute_rounded, 'cm')),
            const SizedBox(width: 12),
            Expanded(
                flex: 2,
                child: _buildMeasurementDropdown<SkirtType>(
                    theme,
                    'نوع',
                    _selectedSkirtType,
                    SkirtType.values,
                    _getSkirtTypeText,
                    (v) => setState(() => _selectedSkirtType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                flex: 3,
                child: _buildMeasurementTextField(
                    theme,
                    _trouserLengthController,
                    'تنبان',
                    Icons.rule_rounded,
                    'cm')),
            const SizedBox(width: 12),
            Expanded(
                flex: 2,
                child: _buildMeasurementDropdown<TrouserType>(
                    theme,
                    'نوع',
                    _selectedTrouserType,
                    TrouserType.values,
                    _getTrouserTypeText,
                    (v) => setState(() => _selectedTrouserType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                child: _buildMeasurementTextField(theme, _hemWidthController,
                    'پاچه', Icons.expand_rounded, 'cm')),
          ]),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('جیب شلوار دارد؟'),
            value: _hasTrouserPocket,
            onChanged: (value) => setState(() => _hasTrouserPocket = value),
            secondary: Icon(_hasTrouserPocket
                ? Icons.check_box_rounded
                : Icons.check_box_outline_blank_rounded),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                    color: Theme.of(context).dividerColor.withOpacity(0.5))),
            tileColor:
                Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
          ),
        ],
      ),
    );
  }

  // --- Helper Widgets for Measurement Section (Reused/adapted from AddOrderScreen) ---

  Widget _buildMeasurementTextField(
    ThemeData theme,
    TextEditingController controller,
    String label,
    IconData icon,
    String suffix,
  ) {
    return TextFormField(
      controller: controller,
      decoration:
          _inputDecoration(theme, label, icon, suffixText: suffix).copyWith(
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      validator: (value) {
        if (value != null &&
            value.isNotEmpty &&
            double.tryParse(value) == null) {
          return 'عدد نامعتبر';
        }
        return null;
      },
    );
  }

  Widget _buildMeasurementDropdown<T extends Enum>(
    ThemeData theme,
    String label,
    T? value,
    List<T> items,
    String Function(T) itemTextBuilder,
    ValueChanged<T?> onChanged,
  ) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: _inputDecoration(theme, label, null).copyWith(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
      ),
      items: items
          .map((T item) => DropdownMenuItem<T>(
                value: item,
                child: Text(itemTextBuilder(item),
                    overflow: TextOverflow.ellipsis),
              ))
          .toList(),
      onChanged: onChanged,
    );
  }

  // --- Enum Text Getters (Keep these, ensure they match AddOrderScreen) ---
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'در انتظار';
      case OrderStatus.inProgress:
        return 'در حال انجام';
      case OrderStatus.completed:
        return 'تکمیل شده';
      case OrderStatus.cancelled:
        return 'لغو شده';
      case OrderStatus.delivered:
        return 'تحویل داده شده';
    }
  }

  String _getSleeveTypeText(SleeveType type) {
    switch (type) {
      case SleeveType.cuffed:
        return 'کفدار';
      case SleeveType.cufflinks:
        return 'کفلینکس';
      case SleeveType.plain:
        return 'ساده';
      case SleeveType.banded:
        return 'بندکدار';
      case SleeveType.boat:
        return 'کشتی';
      case SleeveType.stitchedEdge:
        return 'لب پخته';
      // Removed old cases
    }
  }

  String _getCollarTypeText(CollarType type) {
    switch (type) {
      case CollarType.hindi:
        return 'هندی';
      case CollarType.pakistani:
        return 'پاکستانی';
      case CollarType.half:
        return 'نیمه';
      case CollarType.reverse:
        return 'چپه';
      case CollarType.qasemi:
        return 'قاسمی';
      // Removed old cases
    }
  }

  String _getSkirtTypeText(SkirtType type) {
    switch (type) {
      case SkirtType.punjabi:
        return 'پنجابی';
      case SkirtType.round:
        return 'گرد';
      // Removed old cases
    }
  }

  String _getTrouserTypeText(TrouserType type) {
    switch (type) {
      case TrouserType.regular:
        return 'معمولی';
      case TrouserType.wide:
        return 'گشاد';
      case TrouserType.tight:
        return 'چسپ';
      // Removed old cases
    }
  }
}
