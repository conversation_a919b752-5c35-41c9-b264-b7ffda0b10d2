import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/auth_repository.dart';
import '../domain/models/business_profile.dart';

part 'auth_provider.g.dart';

@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  return AuthRepository();
}

@riverpod
class Auth extends _$Auth {
  @override
  Future<(User?, BusinessProfile?)> build() async {
    final authRepository = ref.watch(authRepositoryProvider);
    final user = FirebaseAuth.instance.currentUser;
    
    if (user == null) return (null, null);
    
    final profile = await authRepository.getBusinessProfile(user.uid);
    return (user, profile);
  }

  Future<void> signIn(String email, String password) async {
    final authRepository = ref.read(authRepositoryProvider);
    await authRepository.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    ref.invalidateSelf();
  }

  Future<void> signOut() async {
    final authRepository = ref.read(authRepositoryProvider);
    await authRepository.signOut();
    ref.invalidateSelf();
  }

  Future<void> register({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phoneNumber,
    String? address,
  }) async {
    final authRepository = ref.read(authRepositoryProvider);
    await authRepository.registerWithEmailAndPassword(
      email: email,
      password: password,
      businessName: businessName,
      ownerName: ownerName,
      phoneNumber: phoneNumber,
      address: address,
    );
    ref.invalidateSelf();
  }
} 