import 'package:flutter/material.dart';
import '../../../../core/presentation/widgets/custom_app_bar.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'راهنما',
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildHelpSection(
            theme,
            'راهنمای کلی',
            [
              _HelpItem(
                question: 'دوخت‌یار چیست؟',
                answer: 'دوخت‌یار یک نرم‌افزار مدیریت خیاطی است که به شما کمک می‌کند تا سفارش‌ها، مشتریان و کسب و کار خود را به صورت حرفه‌ای مدیریت کنید.',
              ),
              _HelpItem(
                question: 'چگونه می‌توانم شروع کنم؟',
                answer: 'برای شروع، می‌توانید از بخش مشتریان، مشتری جدید اضافه کنید و سپس برای آن‌ها سفارش ثبت کنید.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildHelpSection(
            theme,
            'مدیریت سفارش‌ها',
            [
              _HelpItem(
                question: 'چگونه سفارش جدید ثبت کنم؟',
                answer: 'از طریق دکمه + در صفحه سفارش‌ها یا صفحه اصلی می‌توانید سفارش جدید ثبت کنید. ابتدا مشتری را انتخاب کرده و سپس جزئیات سفارش را وارد کنید.',
              ),
              _HelpItem(
                question: 'چگونه وضعیت سفارش را تغییر دهم؟',
                answer: 'در صفحه جزئیات سفارش می‌توانید وضعیت سفارش را به روز کنید. همچنین می‌توانید از طریق لیست سفارش‌ها با کشیدن به چپ، وضعیت را تغییر دهید.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildHelpSection(
            theme,
            'مدیریت مشتریان',
            [
              _HelpItem(
                question: 'چگونه مشتری جدید اضافه کنم؟',
                answer: 'از طریق دکمه + در صفحه مشتریان می‌توانید مشتری جدید اضافه کنید. اطلاعات تماس و اندازه‌های مشتری را وارد کنید.',
              ),
              _HelpItem(
                question: 'چگونه اندازه‌های مشتری را ویرایش کنم؟',
                answer: 'در صفحه جزئیات مشتری، بخش اندازه‌ها را انتخاب کرده و اندازه‌های جدید را وارد کنید.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildHelpSection(
            theme,
            'تنظیمات و پشتیبانی',
            [
              _HelpItem(
                question: 'چگونه اطلاعات کسب و کارم را ویرایش کنم؟',
                answer: 'از طریق منوی تنظیمات، گزینه "پروفایل کسب و کار" را انتخاب کرده و اطلاعات را ویرایش کنید.',
              ),
              _HelpItem(
                question: 'چگونه با پشتیبانی تماس بگیرم؟',
                answer: 'برای تماس با پشتیبانی می‟توانید از طریق ایمیل <EMAIL> با ما در ارتباط باشید.',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection(ThemeData theme, String title, List<_HelpItem> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...items.map((item) => _buildExpansionTile(theme, item)).toList(),
      ],
    );
  }

  Widget _buildExpansionTile(ThemeData theme, _HelpItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        title: Text(
          item.question,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.answer,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
}

class _HelpItem {
  final String question;
  final String answer;

  const _HelpItem({
    required this.question,
    required this.answer,
  });
} 