// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customers_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$customersRepositoryHash() =>
    r'2b0234f56fa292c99c6c16d18b3d747bae2ef67c';

/// See also [customersRepository].
@ProviderFor(customersRepository)
final customersRepositoryProvider =
    AutoDisposeProvider<CustomersRepository>.internal(
  customersRepository,
  name: r'customersRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customersRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CustomersRepositoryRef = AutoDisposeProviderRef<CustomersRepository>;
String _$customersHash() => r'16bad37c962be8f2b609de33e9712aad113bbe99';

/// See also [customers].
@ProviderFor(customers)
final customersProvider = AutoDisposeStreamProvider<List<Customer>>.internal(
  customers,
  name: r'customersProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$customersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CustomersRef = AutoDisposeStreamProviderRef<List<Customer>>;
String _$customersNotifierHash() => r'ded26d5eb9d6ca4ef0ebfc589c3d619f5e0af8aa';

/// See also [CustomersNotifier].
@ProviderFor(CustomersNotifier)
final customersNotifierProvider =
    AutoDisposeAsyncNotifierProvider<CustomersNotifier, void>.internal(
  CustomersNotifier.new,
  name: r'customersNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customersNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CustomersNotifier = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
