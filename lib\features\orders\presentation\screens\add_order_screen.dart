import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shamsi_date/shamsi_date.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:persian_datetime_picker/persian_datetime_picker.dart';
import '../../domain/models/order.dart';
import '../../domain/models/measurement.dart';
import '../../providers/orders_provider.dart';
import '../../../customers/providers/customers_provider.dart';
import '../../../customers/domain/models/customer.dart';
import '../../../customers/presentation/screens/add_customer_screen.dart';
import 'package:collection/collection.dart';
import 'dart:math';
import 'package:tailor_flutter/core/widgets/themed_card.dart';

class AddOrderScreen extends ConsumerStatefulWidget {
  final Order? orderToCopy;

  const AddOrderScreen({super.key, this.orderToCopy});

  @override
  ConsumerState<AddOrderScreen> createState() => _AddOrderScreenState();
}

class _AddOrderScreenState extends ConsumerState<AddOrderScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _receiptNumberController = TextEditingController();
  final _priceController = TextEditingController();
  final _notesController = TextEditingController();
  String? _selectedCustomerId;
  DateTime? _orderDate;
  DateTime? _deliveryDate;
  OrderStatus _status = OrderStatus.pending;
  bool _isPaid = false;
  final _scrollController = ScrollController();
  late TabController _tabController;

  // Measurement fields
  final _heightController = TextEditingController();
  final _sleeveLengthController = TextEditingController();
  SleeveType? _selectedSleeveType;
  final _shoulderWidthController = TextEditingController();
  final _neckCircumferenceController = TextEditingController();
  CollarType? _selectedCollarType;
  final _sideWidthController = TextEditingController();
  final _skirtLengthController = TextEditingController();
  SkirtType? _selectedSkirtType;
  final _trouserLengthController = TextEditingController();
  TrouserType? _selectedTrouserType;
  final _hemWidthController = TextEditingController();
  bool _hasTrouserPocket = false;

  // Add state variables for copy mode
  bool _isCopyMode = false;
  Customer? _copiedCustomer;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _isCopyMode = widget.orderToCopy != null;

    // Always set order date to current date
    _orderDate = DateTime.now();
    
    // Always generate new receipt number
    _receiptNumberController.text = _generateReceiptNumber();

    if (_isCopyMode) {
      // --- Pre-fill fields if copying ---
      final order = widget.orderToCopy!;
      _copiedCustomer = order.customer;
      _selectedCustomerId = order.customerId;
      _priceController.text = order.price.toString();
      _notesController.text = order.notes ?? '';
      _deliveryDate = DateTime.now().add(const Duration(days: 7)); // Set default delivery date
      _status = OrderStatus.pending;
      _isPaid = false;

      if (order.measurement != null) {
        final m = order.measurement!;
        _heightController.text = m.height?.toString() ?? '';
        _sleeveLengthController.text = m.sleeveLength?.toString() ?? '';
        _selectedSleeveType = m.sleeveType;
        _shoulderWidthController.text = m.shoulderWidth?.toString() ?? '';
        _neckCircumferenceController.text = m.neckCircumference?.toString() ?? '';
        _selectedCollarType = m.collarType;
        _sideWidthController.text = m.sideWidth?.toString() ?? '';
        _skirtLengthController.text = m.skirtLength?.toString() ?? '';
        _selectedSkirtType = m.skirtType;
        _trouserLengthController.text = m.trouserLength?.toString() ?? '';
        _selectedTrouserType = m.trouserType;
        _hemWidthController.text = m.hemWidth?.toString() ?? '';
        _hasTrouserPocket = m.hasTrouserPocket ?? false;
      } else {
        _selectedSleeveType = SleeveType.plain;
        _selectedCollarType = CollarType.pakistani;
        _selectedSkirtType = SkirtType.punjabi;
        _selectedTrouserType = TrouserType.regular;
      }
    } else {
      // --- Default behavior for new order ---
      _deliveryDate = DateTime.now().add(const Duration(days: 7));
      _selectedSleeveType = SleeveType.plain;
      _selectedCollarType = CollarType.pakistani;
      _selectedSkirtType = SkirtType.punjabi;
      _selectedTrouserType = TrouserType.regular;
    }
  }

  String _generateReceiptNumber() {
    final now = Jalali.now();
    final formatter = now.formatter;
    final year = formatter.yyyy;
    final month = formatter.mm;
    final day = formatter.dd;
    final random = Random();
    final randomNumber = 1000 + random.nextInt(9000);
    return '$year$month$day-$randomNumber';
  }

  String _formatPersianDisplayDate(DateTime? date) {
    if (date == null) return 'انتخاب نشده';
    final jalali = Jalali.fromDateTime(date);
    final formatter = jalali.formatter;
    return '${formatter.wN} ${formatter.d} ${formatter.mN} ${formatter.yyyy}';
  }

  Future<void> _selectDate(BuildContext context, bool isOrderDate) async {
    final Jalali? picked = await showPersianDatePicker(
      context: context,
      initialDate: Jalali.fromDateTime(isOrderDate ? (_orderDate ?? DateTime.now()) : (_deliveryDate ?? DateTime.now())),
      firstDate: Jalali(1380),
      lastDate: Jalali(1450),
    );

    if (picked != null) {
      setState(() {
        final pickedDateTime = picked.toDateTime();
        if (isOrderDate) {
          _orderDate = pickedDateTime;
        } else {
          _deliveryDate = pickedDateTime;
        }
      });
    }
  }

  Future<void> _addOrder() async {
    if (_selectedCustomerId == null && !_isCopyMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لطفا مشتری را انتخاب کنید'), behavior: SnackBarBehavior.floating),
      );
      _tabController.animateTo(0);
      return;
    }
    if (_deliveryDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لطفا تاریخ تحویل را انتخاب کنید'), behavior: SnackBarBehavior.floating),
      );
      _tabController.animateTo(0);
      return;
    }

    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لطفا خطاهای فرم را در هر دو تب بررسی کنید'), behavior: SnackBarBehavior.floating),
      );
      return;
    }

    setState(() => _isLoading = true);

    final measurement = Measurement(
      height: double.tryParse(_heightController.text.trim()) ?? 0.0,
      sleeveLength: double.tryParse(_sleeveLengthController.text.trim()) ?? 0.0,
      sleeveType: _selectedSleeveType ?? SleeveType.plain,
      shoulderWidth: double.tryParse(_shoulderWidthController.text.trim()) ?? 0.0,
      neckCircumference: double.tryParse(_neckCircumferenceController.text.trim()) ?? 0.0,
      collarType: _selectedCollarType ?? CollarType.pakistani,
      sideWidth: double.tryParse(_sideWidthController.text.trim()) ?? 0.0,
      skirtLength: double.tryParse(_skirtLengthController.text.trim()) ?? 0.0,
      skirtType: _selectedSkirtType ?? SkirtType.punjabi,
      trouserLength: double.tryParse(_trouserLengthController.text.trim()) ?? 0.0,
      trouserType: _selectedTrouserType ?? TrouserType.regular,
      hemWidth: double.tryParse(_hemWidthController.text.trim()) ?? 0.0,
      hasTrouserPocket: _hasTrouserPocket,
    );

    try {
      await ref.read(ordersNotifierProvider.notifier).addOrder(
            customerId: _selectedCustomerId!,
            receiptNumber: _receiptNumberController.text,
            price: double.parse(_priceController.text.trim()),
            orderDate: _orderDate!,
            deliveryDate: _deliveryDate!,
            status: _status,
            notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
            isPaid: _isPaid,
            measurement: measurement,
          );
      if (mounted) {
        // Invalidate both providers to ensure UI updates
        ref.invalidate(ordersProvider);
        ref.invalidate(customersProvider);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isCopyMode ? 'سفارش با موفقیت کپی شد' : 'سفارش با موفقیت ثبت شد'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Pop until we reach the orders screen
        if (_isCopyMode) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        } else {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطا در ثبت سفارش: ${e.toString()}'),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _receiptNumberController.dispose();
    _priceController.dispose();
    _notesController.dispose();
    _heightController.dispose();
    _sleeveLengthController.dispose();
    _shoulderWidthController.dispose();
    _neckCircumferenceController.dispose();
    _sideWidthController.dispose();
    _skirtLengthController.dispose();
    _trouserLengthController.dispose();
    _hemWidthController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customersState = ref.watch(customersProvider);
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(_isCopyMode ? 'کپی کردن سفارش' : 'سفارش جدید'),
          elevation: 1,
          shadowColor: theme.shadowColor.withOpacity(0.1),
          surfaceTintColor: Colors.transparent,
          bottom: TabBar(
            controller: _tabController,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: const [
              Tab(
                child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [Icon(Icons.receipt_long_outlined, size: 20), SizedBox(width: 8), Text('مشخصات')]),
              ),
              Tab(
                child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [Icon(Icons.straighten_outlined, size: 20), SizedBox(width: 8), Text('اندازه‌ها')]),
              ),
            ],
          ),
        ),
        body: customersState.when(
          data: (customers) {
            if (_isCopyMode && _selectedCustomerId != null && !customers.any((c) => c.id == _selectedCustomerId)) {
              _copiedCustomer = null;
            }
            if (_isCopyMode && _copiedCustomer == null && _selectedCustomerId != null) {
                _copiedCustomer = customers.firstWhereOrNull((c) => c.id == _selectedCustomerId);
             }

            if (!_isCopyMode && customers.isEmpty) {
              return _buildEmptyCustomerState(context, theme);
            }

            return Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOrderInfoTab(context, theme, textTheme, customers),
                        _buildMeasurementsTab(context, theme, textTheme),
                      ],
                    ),
                  ),
                  _buildSubmitButtonArea(context, theme),
                ],
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(context, theme, error),
        ),
      ),
    );
  }

  InputDecoration _inputDecoration(ThemeData theme, String label, IconData? icon, {String? suffixText}) {
    final border = OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide(color: theme.dividerColor),
    );
    return InputDecoration(
      labelText: label,
      prefixIcon: icon != null ? Icon(icon, size: 20) : null,
      suffixText: suffixText,
      border: border,
      enabledBorder: border,
      focusedBorder: border.copyWith(borderSide: BorderSide(color: theme.colorScheme.primary, width: 2)),
      errorBorder: border.copyWith(borderSide: BorderSide(color: theme.colorScheme.error, width: 1)),
      focusedErrorBorder: border.copyWith(borderSide: BorderSide(color: theme.colorScheme.error, width: 2)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    );
  }

  Widget _buildEmptyCustomerState(BuildContext context, ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline_rounded,
              size: 80,
              color: theme.colorScheme.secondary.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'هیچ مشتری ثبت نشده است',
              style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'برای ثبت سفارش، ابتدا باید یک مشتری اضافه کنید.',
              style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddCustomerScreen(),
                  ),
                ).then((_) => ref.refresh(customersProvider));
              },
              icon: const Icon(Icons.person_add_alt_1_rounded),
              label: const Text('افزودن مشتری جدید'),
              style: FilledButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, ThemeData theme, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطا در بارگیری اطلاعات',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            TextButton.icon(
              onPressed: () => ref.refresh(customersProvider),
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('تلاش مجدد'),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButtonArea(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 16, 16 + MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: FilledButton.icon(
        onPressed: _isLoading ? null : _addOrder,
        icon: _isLoading
            ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: theme.colorScheme.onPrimary))
            : Icon(_isCopyMode ? Icons.copy_rounded : Icons.save_rounded),
        label: Text(_isLoading ? 'در حال ثبت...' : (_isCopyMode ? 'کپی و ثبت سفارش' : 'ثبت سفارش')),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 14),
          textStyle: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          minimumSize: const Size(double.infinity, 50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderInfoTab(BuildContext context, ThemeData theme, TextTheme textTheme, List<Customer> customers) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      children: [
        _buildCustomerSection(context, theme, textTheme, customers),
        const SizedBox(height: 24),
        _buildOrderDetailsSection(context, theme, textTheme),
      ],
    );
  }

  Widget _buildMeasurementsTab(BuildContext context, ThemeData theme, TextTheme textTheme) {
    return ListView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      children: [
        _buildMeasurementSection(context, theme, textTheme),
      ],
    );
  }

  Widget _buildOrderDetailsSection(BuildContext context, ThemeData theme, TextTheme textTheme) {
    return ThemedCard(
      title: 'اطلاعات سفارش',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Receipt Number (Read-only)
          InputDecorator(
            decoration: _inputDecoration(theme, 'شماره رسید', Icons.receipt_long_outlined).copyWith(
              fillColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
              filled: true,
            ),
            child: Text(
              _receiptNumberController.text,
              style: textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _priceController,
            decoration: _inputDecoration(theme, 'قیمت', Icons.attach_money_rounded),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value == null || value.isEmpty) return 'لطفا قیمت را وارد کنید';
              if (double.tryParse(value) == null) return 'لطفا عدد معتبر وارد کنید';
              return null;
            },
          ),
          const SizedBox(height: 16),
          // Order Date (Read-only)
          InputDecorator(
            decoration: _inputDecoration(theme, 'تاریخ سفارش', Icons.calendar_today_outlined).copyWith(
              fillColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
              filled: true,
            ),
            child: Text(
              _formatPersianDisplayDate(_orderDate),
              style: textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () => _selectDate(context, false),
            child: InputDecorator(
              decoration: _inputDecoration(theme, 'تاریخ تحویل', Icons.local_shipping_outlined),
              child: Text(_formatPersianDisplayDate(_deliveryDate), style: textTheme.bodyLarge),
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<OrderStatus>(
            value: _status,
            decoration: _inputDecoration(theme, 'وضعیت سفارش', Icons.list_alt_outlined),
            items: OrderStatus.values.map((status) => DropdownMenuItem(
                value: status,
                child: Text(_getStatusText(status))
            )).toList(),
            onChanged: (value) => setState(() => _status = value!),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('وضعیت پرداخت'),
            value: _isPaid,
            onChanged: (value) => setState(() => _isPaid = value),
            secondary: Icon(_isPaid ? Icons.check_circle_outline_rounded : Icons.highlight_off_rounded,
               color: _isPaid ? Colors.green : theme.colorScheme.onSurfaceVariant),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: theme.dividerColor.withOpacity(0.5))
            ),
            tileColor: theme.colorScheme.surfaceVariant.withOpacity(0.2),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: _inputDecoration(theme, 'یادداشت‌ها (اختیاری)', Icons.note_alt_outlined),
            maxLines: 3,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSection(BuildContext context, ThemeData theme, TextTheme textTheme, List<Customer> customers) {
    if (_isCopyMode) {
      return ThemedCard(
        title: 'مشتری (کپی شده)',
        trailing: Icon(Icons.lock_outline, size: 18, color: theme.disabledColor),
        child: _copiedCustomer == null
            ? const ListTile(title: Text('مشتری اصلی یافت نشد'), leading: Icon(Icons.error_outline))
            : ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.secondaryContainer,
                  child: Icon(Icons.person_pin_rounded, color: theme.colorScheme.onSecondaryContainer),
                ),
                title: Text(_copiedCustomer!.name, style: textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
                subtitle: Text(_copiedCustomer!.phoneNumber),
                contentPadding: EdgeInsets.zero,
              ),
      );
    } else {
      final initiallySelectedCustomer = customers.firstWhereOrNull((c) => c.id == _selectedCustomerId);
      return ThemedCard(
        title: 'مشتری',
        trailing: TextButton.icon(
            icon: const Icon(Icons.person_add_alt_1_rounded, size: 20),
            label: const Text('جدید'),
            style: TextButton.styleFrom(padding: EdgeInsets.zero),
            onPressed: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const AddCustomerScreen()))
                    .then((_) => ref.refresh(customersProvider));
            },
        ),
        child: DropdownSearch<Customer>(
          popupProps: PopupProps.menu(
            showSearchBox: true,
            searchFieldProps: TextFieldProps(
              textDirection: TextDirection.rtl,
              decoration: _inputDecoration(theme, 'جستجو...', null).copyWith(
                 prefixIcon: const Icon(Icons.search, size: 20)
              ),
            ),
            itemBuilder: (context, customer, isSelected) => ListTile(
              title: Text(customer.name),
              subtitle: Text(customer.phoneNumber),
              selected: isSelected,
            ),
            emptyBuilder: (context, searchEntry) => const ListTile(title: Center(child: Text('مشتری یافت نشد'))),
            constraints: const BoxConstraints(maxHeight: 350),
            menuProps: MenuProps(borderRadius: BorderRadius.circular(12)),
          ),
          items: customers,
          itemAsString: (Customer c) => c.name,
          selectedItem: initiallySelectedCustomer,
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: _inputDecoration(theme, 'انتخاب مشتری', Icons.person_outline_rounded).copyWith(
               contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4)
            ),
          ),
          onChanged: (Customer? customer) => setState(() => _selectedCustomerId = customer?.id),
          validator: (Customer? item) => item == null ? "لطفا مشتری را انتخاب کنید" : null,
        ),
      );
    }
  }

  Widget _buildMeasurementSection(BuildContext context, ThemeData theme, TextTheme textTheme) {
    return ThemedCard(
      title: 'اندازه‌گیری‌ها',
      child: Column(
        children: [
          Row(children: [
             Expanded(child: _buildMeasurementTextField(theme, _heightController, 'قد', Icons.height_rounded, 'cm')),
             const SizedBox(width: 12),
             Expanded(child: _buildMeasurementTextField(theme, _shoulderWidthController, 'شانه', Icons.square_foot_rounded, 'cm')),
          ]),
          const SizedBox(height: 16),
          Row(children: [
             Expanded(flex: 3, child: _buildMeasurementTextField(theme, _sleeveLengthController, 'آستین', Icons.straighten_rounded, 'cm')),
             const SizedBox(width: 12),
             Expanded(flex: 2, child: _buildMeasurementDropdown<SleeveType>(theme, 'نوع', _selectedSleeveType, SleeveType.values, _getSleeveTypeText, (v) => setState(() => _selectedSleeveType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
             Expanded(flex: 3, child: _buildMeasurementTextField(theme, _neckCircumferenceController, 'یخن', Icons.circle_outlined, 'cm')),
             const SizedBox(width: 12),
             Expanded(flex: 2, child: _buildMeasurementDropdown<CollarType>(theme, 'نوع', _selectedCollarType, CollarType.values, _getCollarTypeText, (v) => setState(() => _selectedCollarType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(child: _buildMeasurementTextField(theme, _sideWidthController, 'بغل', Icons.width_normal_rounded, 'cm')),
          ]),
          const SizedBox(height: 16),
          Row(children: [
             Expanded(flex: 3, child: _buildMeasurementTextField(theme, _skirtLengthController, 'دامن', Icons.vertical_distribute_rounded, 'cm')),
             const SizedBox(width: 12),
             Expanded(flex: 2, child: _buildMeasurementDropdown<SkirtType>(theme, 'نوع', _selectedSkirtType, SkirtType.values, _getSkirtTypeText, (v) => setState(() => _selectedSkirtType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
             Expanded(flex: 3, child: _buildMeasurementTextField(theme, _trouserLengthController, 'تنبان', Icons.rule_rounded, 'cm')),
             const SizedBox(width: 12),
             Expanded(flex: 2, child: _buildMeasurementDropdown<TrouserType>(theme, 'نوع', _selectedTrouserType, TrouserType.values, _getTrouserTypeText, (v) => setState(() => _selectedTrouserType = v))),
          ]),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(child: _buildMeasurementTextField(theme, _hemWidthController, 'پاچه', Icons.expand_rounded, 'cm')),
          ]),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text(' تنبان جیب دارد؟'),
            value: _hasTrouserPocket,
            onChanged: (value) => setState(() => _hasTrouserPocket = value),
            secondary: Icon(_hasTrouserPocket ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: theme.dividerColor.withOpacity(0.5))
            ),
            tileColor: theme.colorScheme.surfaceVariant.withOpacity(0.2),
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementTextField(
     ThemeData theme,
     TextEditingController controller,
     String label,
     IconData icon,
     String suffix,
   ) {
     return TextFormField(
       controller: controller,
       decoration: _inputDecoration(theme, label, icon, suffixText: suffix).copyWith(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
       ),
       keyboardType: const TextInputType.numberWithOptions(decimal: true),
       validator: (value) {
         if (value != null && value.isNotEmpty && double.tryParse(value) == null) {
           return 'عدد نامعتبر';
         }
         return null;
       },
     );
   }

  Widget _buildMeasurementDropdown<T extends Enum>(
     ThemeData theme,
     String label,
     T? value,
     List<T> items,
     String Function(T) itemTextBuilder,
     ValueChanged<T?> onChanged,
   ) {
     return DropdownButtonFormField<T>(
       value: value,
       decoration: _inputDecoration(theme, label, null).copyWith(
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
       ),
       items: items.map((T item) => DropdownMenuItem<T>(
         value: item,
         child: Text(itemTextBuilder(item), overflow: TextOverflow.ellipsis),
       )).toList(),
       onChanged: onChanged,
     );
   }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'در انتظار';
      case OrderStatus.inProgress:
        return 'در حال انجام';
      case OrderStatus.completed:
        return 'تکمیل شده';
      case OrderStatus.cancelled:
        return 'لغو شده';
      case OrderStatus.delivered:
        return 'تحویل داده شده';
    }
  }

  String _getSleeveTypeText(SleeveType type) {
    switch (type) {
      case SleeveType.cuffed:
        return 'کفدار';
      case SleeveType.cufflinks:
        return 'کفلینکس';
      case SleeveType.plain:
        return 'ساده';
      case SleeveType.banded:
        return 'بندکدار';
      case SleeveType.boat:
        return 'کشتی';
      case SleeveType.stitchedEdge:
        return 'لب پخته';
    }
  }

  String _getCollarTypeText(CollarType type) {
    switch (type) {
      case CollarType.hindi:
        return 'هندی';
      case CollarType.pakistani:
        return 'پاکستانی';
      case CollarType.half:
        return 'نیمه';
      case CollarType.reverse:
        return 'چپه';
      case CollarType.qasemi:
        return 'قاسمی';
    }
  }

  String _getSkirtTypeText(SkirtType type) {
    switch (type) {
      case SkirtType.punjabi:
        return 'پنجابی';
      case SkirtType.round:
        return 'گرد';
    }
  }

  String _getTrouserTypeText(TrouserType type) {
    switch (type) {
      case TrouserType.regular:
        return 'معمولی';
      case TrouserType.wide:
        return 'گشاد';
      case TrouserType.tight:
        return 'چسپ';
    }
  }
} 