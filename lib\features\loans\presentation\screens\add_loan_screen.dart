import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:persian_datetime_picker/persian_datetime_picker.dart';

import '../../providers/loans_provider.dart';
import '../../../customers/providers/customers_provider.dart';
import '../../../customers/domain/models/customer.dart';
import '../../../customers/presentation/screens/add_customer_screen.dart';
import 'dart:math';

class AddLoanScreen extends ConsumerStatefulWidget {
  final String? customerId;

  const AddLoanScreen({
    super.key,
    this.customerId,
  });

  @override
  ConsumerState<AddLoanScreen> createState() => _AddLoanScreenState();
}

class _AddLoanScreenState extends ConsumerState<AddLoanScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loanNumberController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  Customer? _selectedCustomer;
  DateTime _createdDate = DateTime.now();
  DateTime? _dueDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _generateLoanNumber();
  }

  @override
  void dispose() {
    _loanNumberController.dispose();
    _totalAmountController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _generateLoanNumber() {
    final now = DateTime.now();
    final random = Random();
    final randomNumber = random.nextInt(9999).toString().padLeft(4, '0');
    _loanNumberController.text =
        '${now.year}${now.month.toString().padLeft(2, '0')}$randomNumber';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customersAsync = ref.watch(customersProvider);

    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('ایجاد وام جدید'),
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.onSurface,
          elevation: 0,
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Customer selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'انتخاب مشتری',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      customersAsync.when(
                        data: (customers) => _buildCustomerDropdown(customers),
                        loading: () =>
                            const Center(child: CircularProgressIndicator()),
                        error: (error, stack) =>
                            Text('خطا در بارگذاری مشتریان: $error'),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Loan details
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'جزئیات وام',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Loan number
                      TextFormField(
                        controller: _loanNumberController,
                        decoration: const InputDecoration(
                          labelText: 'شماره وام',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'شماره وام الزامی است';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Total amount
                      TextFormField(
                        controller: _totalAmountController,
                        decoration: const InputDecoration(
                          labelText: 'مبلغ کل وام (تومان)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.attach_money),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'مبلغ وام الزامی است';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount <= 0) {
                            return 'مبلغ وام باید عددی مثبت باشد';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'توضیحات وام',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.description),
                        ),
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Dates
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تاریخ‌ها',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Created date
                      _buildDateField(
                        'تاریخ ایجاد',
                        _createdDate,
                        Icons.calendar_today,
                        (date) => setState(() => _createdDate = date),
                      ),

                      const SizedBox(height: 16),

                      // Due date
                      _buildDateField(
                        'تاریخ سررسید (اختیاری)',
                        _dueDate,
                        Icons.schedule,
                        (date) => setState(() => _dueDate = date),
                        isOptional: true,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Notes
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'یادداشت‌ها',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'یادداشت‌های اضافی',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Submit button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitForm,
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : const Text('ایجاد وام'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerDropdown(List<Customer> customers) {
    return Column(
      children: [
        DropdownSearch<Customer>(
          items: customers,
          itemAsString: (customer) => customer.name,
          selectedItem: _selectedCustomer,
          onChanged: (customer) {
            setState(() {
              _selectedCustomer = customer;
            });
          },
          dropdownDecoratorProps: const DropDownDecoratorProps(
            dropdownSearchDecoration: InputDecoration(
              labelText: 'انتخاب مشتری',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
          ),
          popupProps: PopupProps.menu(
            showSearchBox: true,
            searchFieldProps: const TextFieldProps(
              decoration: InputDecoration(
                hintText: 'جستجو مشتری...',
                prefixIcon: Icon(Icons.search),
              ),
            ),
          ),
          validator: (customer) {
            if (customer == null) {
              return 'انتخاب مشتری الزامی است';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        TextButton.icon(
          onPressed: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const AddCustomerScreen()),
            );
            if (result == true) {
              // Refresh customers list
              ref.refresh(customersProvider);
            }
          },
          icon: const Icon(Icons.add),
          label: const Text('افزودن مشتری جدید'),
        ),
      ],
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    IconData icon,
    Function(DateTime) onDateSelected, {
    bool isOptional = false,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: () => _selectDate(onDateSelected, date),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          prefixIcon: Icon(icon),
          suffixIcon: isOptional && date != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onDateSelected(DateTime.now()),
                )
              : null,
        ),
        child: Text(
          date != null
              ? Jalali.fromDateTime(date).formatCompactDate()
              : isOptional
                  ? 'انتخاب نشده'
                  : 'انتخاب تاریخ',
          style: theme.textTheme.bodyLarge,
        ),
      ),
    );
  }

  Future<void> _selectDate(
      Function(DateTime) onDateSelected, DateTime? currentDate) async {
    final picked = await showPersianDatePicker(
      context: context,
      initialDate:
          currentDate != null ? Jalali.fromDateTime(currentDate) : Jalali.now(),
      firstDate: Jalali(1400),
      lastDate: Jalali(1450),
    );

    if (picked != null) {
      onDateSelected(picked.toDateTime());
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(loansNotifierProvider.notifier).addLoan(
            customerId: _selectedCustomer!.id,
            loanNumber: _loanNumberController.text.trim(),
            totalAmount: double.parse(_totalAmountController.text.trim()),
            createdDate: _createdDate,
            dueDate: _dueDate,
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('وام با موفقیت ایجاد شد'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در ایجاد وام: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
