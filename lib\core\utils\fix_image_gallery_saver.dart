import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';

/// جایگزین برای image_gallery_saver
/// این کلاس به جای استفاده از پکیج image_gallery_saver، تصویر را در پوشه موقت ذخیره می‌کند
/// و سپس با استفاده از share_plus آن را به اشتراک می‌گذارد
class ImageSaver {
  /// ذخیره تصویر در گالری
  static Future<Map<String, dynamic>> saveImage(
    Uint8List imageBytes, {
    int quality = 80,
    String? name,
    bool isReturnImagePathOfIOS = false,
  }) async {
    try {
      // ذخیره تصویر در پوشه موقت
      final tempDir = await getTemporaryDirectory();
      final fileName = name ?? 'image_${DateTime.now().millisecondsSinceEpoch}';
      final filePath = '${tempDir.path}/$fileName.png';
      final file = await File(filePath).create();
      await file.writeAsBytes(imageBytes);

      // استفاده از share_plus برای ذخیره تصویر
      try {
        final xFile = XFile(file.path, mimeType: 'image/png');
        final params = ShareParams(
          files: [xFile],
          text: 'ذخیره تصویر در گالری',
        );

        final result = await SharePlus.instance.share(params);

        return {
          'isSuccess': result.status == ShareResultStatus.success,
          'filePath': file.path,
        };
      } catch (e) {
        return {
          'isSuccess': false,
          'errorMessage': e.toString(),
        };
      }
    } catch (e) {
      return {
        'isSuccess': false,
        'errorMessage': e.toString(),
      };
    }
  }
}
