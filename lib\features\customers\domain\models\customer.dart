import 'package:freezed_annotation/freezed_annotation.dart';

part 'customer.freezed.dart';
part 'customer.g.dart';

@freezed
class Customer with _$Customer {
  const factory Customer({
    required String id,
    required String name,
    required String phoneNumber,
    String? address,
    String? email,
    String? notes,
    @Default(0) int totalOrders,
    @Default(0) int activeOrders,
    @Default(0.0) double totalSpent,
  }) = _Customer;

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);
} 