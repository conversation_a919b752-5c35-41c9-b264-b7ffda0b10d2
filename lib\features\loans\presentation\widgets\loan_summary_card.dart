import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/models/loan_summary.dart';

class LoanSummaryCard extends StatelessWidget {
  final LoanSummary summary;

  const LoanSummaryCard({
    super.key,
    required this.summary,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_IR',
      symbol: 'تومان',
      decimalDigits: 0,
    );

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Top row - loan counts
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'کل وام‌ها',
                  summary.totalLoans.toString(),
                  Icons.account_balance_wallet,
                  theme.colorScheme.primary,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'فعال',
                  summary.activeLoans.toString(),
                  Icons.trending_up,
                  theme.colorScheme.secondary,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'معوقه',
                  summary.overdueLoans.toString(),
                  Icons.warning,
                  theme.colorScheme.error,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'تکمیل شده',
                  summary.completedLoans.toString(),
                  Icons.check_circle,
                  theme.colorScheme.tertiary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Bottom row - amounts
          Row(
            children: [
              Expanded(
                child: _buildAmountCard(
                  context,
                  'کل مبلغ وام‌ها',
                  summary.totalLoanAmount,
                  currencyFormat,
                  theme.colorScheme.primary,
                  Icons.account_balance,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildAmountCard(
                  context,
                  'مبلغ باقی‌مانده',
                  summary.totalOutstandingAmount,
                  currencyFormat,
                  theme.colorScheme.secondary,
                  Icons.pending_actions,
                ),
              ),
            ],
          ),
          
          if (summary.totalOverdueAmount > 0) ...[
            const SizedBox(height: 8),
            _buildAmountCard(
              context,
              'مبلغ معوقه',
              summary.totalOverdueAmount,
              currencyFormat,
              theme.colorScheme.error,
              Icons.warning_amber,
              isFullWidth: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountCard(
    BuildContext context,
    String title,
    double amount,
    NumberFormat formatter,
    Color color,
    IconData icon, {
    bool isFullWidth = false,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: isFullWidth ? double.infinity : null,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              formatter.format(amount),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
