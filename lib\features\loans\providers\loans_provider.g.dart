// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loans_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loansRepositoryHash() => r'e00c19f5027401fcb49b3ded9431a66707f4c0b3';

/// See also [loansRepository].
@ProviderFor(loansRepository)
final loansRepositoryProvider = AutoDisposeProvider<LoansRepository>.internal(
  loansRepository,
  name: r'loansRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loansRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LoansRepositoryRef = AutoDisposeProviderRef<LoansRepository>;
String _$loansHash() => r'390683fc877d5ae9f68540a6d6f40484784c2c26';

/// See also [loans].
@ProviderFor(loans)
final loansProvider = AutoDisposeStreamProvider<List<Loan>>.internal(
  loans,
  name: r'loansProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loansHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LoansRef = AutoDisposeStreamProviderRef<List<Loan>>;
String _$customerLoansHash() => r'ca70a063212d2a1228c085aabec8563dda294097';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [customerLoans].
@ProviderFor(customerLoans)
const customerLoansProvider = CustomerLoansFamily();

/// See also [customerLoans].
class CustomerLoansFamily extends Family<AsyncValue<List<Loan>>> {
  /// See also [customerLoans].
  const CustomerLoansFamily();

  /// See also [customerLoans].
  CustomerLoansProvider call(
    String customerId,
  ) {
    return CustomerLoansProvider(
      customerId,
    );
  }

  @override
  CustomerLoansProvider getProviderOverride(
    covariant CustomerLoansProvider provider,
  ) {
    return call(
      provider.customerId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'customerLoansProvider';
}

/// See also [customerLoans].
class CustomerLoansProvider extends AutoDisposeStreamProvider<List<Loan>> {
  /// See also [customerLoans].
  CustomerLoansProvider(
    String customerId,
  ) : this._internal(
          (ref) => customerLoans(
            ref as CustomerLoansRef,
            customerId,
          ),
          from: customerLoansProvider,
          name: r'customerLoansProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$customerLoansHash,
          dependencies: CustomerLoansFamily._dependencies,
          allTransitiveDependencies:
              CustomerLoansFamily._allTransitiveDependencies,
          customerId: customerId,
        );

  CustomerLoansProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.customerId,
  }) : super.internal();

  final String customerId;

  @override
  Override overrideWith(
    Stream<List<Loan>> Function(CustomerLoansRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CustomerLoansProvider._internal(
        (ref) => create(ref as CustomerLoansRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        customerId: customerId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Loan>> createElement() {
    return _CustomerLoansProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CustomerLoansProvider && other.customerId == customerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, customerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CustomerLoansRef on AutoDisposeStreamProviderRef<List<Loan>> {
  /// The parameter `customerId` of this provider.
  String get customerId;
}

class _CustomerLoansProviderElement
    extends AutoDisposeStreamProviderElement<List<Loan>> with CustomerLoansRef {
  _CustomerLoansProviderElement(super.provider);

  @override
  String get customerId => (origin as CustomerLoansProvider).customerId;
}

String _$loanPaymentsHash() => r'c0dd008161aa65a7b3b902f91721c82dc7b7501b';

/// See also [loanPayments].
@ProviderFor(loanPayments)
const loanPaymentsProvider = LoanPaymentsFamily();

/// See also [loanPayments].
class LoanPaymentsFamily extends Family<AsyncValue<List<Payment>>> {
  /// See also [loanPayments].
  const LoanPaymentsFamily();

  /// See also [loanPayments].
  LoanPaymentsProvider call(
    String loanId,
  ) {
    return LoanPaymentsProvider(
      loanId,
    );
  }

  @override
  LoanPaymentsProvider getProviderOverride(
    covariant LoanPaymentsProvider provider,
  ) {
    return call(
      provider.loanId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'loanPaymentsProvider';
}

/// See also [loanPayments].
class LoanPaymentsProvider extends AutoDisposeStreamProvider<List<Payment>> {
  /// See also [loanPayments].
  LoanPaymentsProvider(
    String loanId,
  ) : this._internal(
          (ref) => loanPayments(
            ref as LoanPaymentsRef,
            loanId,
          ),
          from: loanPaymentsProvider,
          name: r'loanPaymentsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$loanPaymentsHash,
          dependencies: LoanPaymentsFamily._dependencies,
          allTransitiveDependencies:
              LoanPaymentsFamily._allTransitiveDependencies,
          loanId: loanId,
        );

  LoanPaymentsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.loanId,
  }) : super.internal();

  final String loanId;

  @override
  Override overrideWith(
    Stream<List<Payment>> Function(LoanPaymentsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LoanPaymentsProvider._internal(
        (ref) => create(ref as LoanPaymentsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        loanId: loanId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<List<Payment>> createElement() {
    return _LoanPaymentsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LoanPaymentsProvider && other.loanId == loanId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, loanId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LoanPaymentsRef on AutoDisposeStreamProviderRef<List<Payment>> {
  /// The parameter `loanId` of this provider.
  String get loanId;
}

class _LoanPaymentsProviderElement
    extends AutoDisposeStreamProviderElement<List<Payment>>
    with LoanPaymentsRef {
  _LoanPaymentsProviderElement(super.provider);

  @override
  String get loanId => (origin as LoanPaymentsProvider).loanId;
}

String _$loanSummaryHash() => r'520511e8b71600b4401ba8fc4c4ea29057730f13';

/// See also [loanSummary].
@ProviderFor(loanSummary)
final loanSummaryProvider = AutoDisposeFutureProvider<LoanSummary>.internal(
  loanSummary,
  name: r'loanSummaryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loanSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LoanSummaryRef = AutoDisposeFutureProviderRef<LoanSummary>;
String _$loansNotifierHash() => r'e71c7b5161239d20c6090f5c1bb52544c456845a';

/// See also [LoansNotifier].
@ProviderFor(LoansNotifier)
final loansNotifierProvider =
    AutoDisposeAsyncNotifierProvider<LoansNotifier, void>.internal(
  LoansNotifier.new,
  name: r'loansNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loansNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoansNotifier = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
