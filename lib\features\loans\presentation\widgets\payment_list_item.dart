import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/models/payment.dart';

class PaymentListItem extends StatelessWidget {
  final Payment payment;
  final VoidCallback? onDelete;

  const PaymentListItem({
    super.key,
    required this.payment,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_IR',
      symbol: 'تومان',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('yyyy/MM/dd', 'fa_IR');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Payment method icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getPaymentMethodColor(payment.method).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getPaymentMethodIcon(payment.method),
                color: _getPaymentMethodColor(payment.method),
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Payment details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Amount
                  Text(
                    currencyFormat.format(payment.amount),
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Date and method
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 14,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        dateFormat.format(payment.paymentDate),
                        style: textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        _getPaymentMethodName(payment.method),
                        style: textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  // Receipt number
                  if (payment.receiptNumber != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.receipt,
                          size: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'رسید: ${payment.receiptNumber}',
                          style: textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                  
                  // Notes
                  if (payment.notes != null && payment.notes!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      payment.notes!,
                      style: textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            // Delete button
            if (onDelete != null)
              IconButton(
                icon: const Icon(Icons.delete_outline),
                color: theme.colorScheme.error,
                onPressed: onDelete,
                tooltip: 'حذف پرداخت',
              ),
          ],
        ),
      ),
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.check:
        return Icons.receipt_long;
      case PaymentMethod.other:
        return Icons.payment;
    }
  }

  Color _getPaymentMethodColor(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Colors.green;
      case PaymentMethod.card:
        return Colors.blue;
      case PaymentMethod.bankTransfer:
        return Colors.purple;
      case PaymentMethod.check:
        return Colors.orange;
      case PaymentMethod.other:
        return Colors.grey;
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدی';
      case PaymentMethod.card:
        return 'کارت';
      case PaymentMethod.bankTransfer:
        return 'انتقال بانکی';
      case PaymentMethod.check:
        return 'چک';
      case PaymentMethod.other:
        return 'سایر';
    }
  }
}
