import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shamsi_date/shamsi_date.dart';

import '../../features/customers/domain/models/customer.dart';
import '../../features/orders/domain/models/measurement.dart';
import '../../features/orders/domain/models/order.dart';

class ImageInvoiceService {
  static final ScreenshotController _screenshotController =
      ScreenshotController();

  /// تولید تصویر فاکتور برای سفارش
  static Future<Uint8List> generateOrderImage(
    Order order,
    BuildContext context,
  ) async {
    final image = await _screenshotController.captureFromWidget(
      _buildInvoiceWidget(order, context),
      delay: const Duration(milliseconds: 100),
      context: context,
      pixelRatio: 3.0, // برای کیفیت بالاتر
    );
    return image;
  }

  /// ذخیره تصویر فاکتور در دستگاه
  static Future<File> saveOrderImage(
      Uint8List imageBytes, String fileName) async {
    final directory = await getTemporaryDirectory();
    final filePath = '${directory.path}/$fileName.png';
    final file = File(filePath);
    await file.writeAsBytes(imageBytes);
    return file;
  }

  /// ساخت ویجت فاکتور
  static Widget _buildInvoiceWidget(Order order, BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_AF',
      symbol: '؋',
      decimalDigits: 0,
    );
    final customer = order.customer;

    return Material(
      color: Colors.white,
      child: Directionality(
        textDirection: ui.TextDirection.rtl,
        child: Container(
          width: 1080, // عرض ثابت برای تصویر
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(order, theme),
              const SizedBox(height: 24),
              _buildCustomerInfo(customer, theme),
              const SizedBox(height: 24),
              _buildOrderDetails(order, currencyFormat, theme),
              const SizedBox(height: 24),
              _buildFooter(theme),
            ],
          ),
        ),
      ),
    );
  }

  /// ساخت بخش هدر فاکتور
  static Widget _buildHeader(Order order, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.design_services_rounded,
              size: 32,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'دوخت‌یار',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'رسید سفارش',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'شماره رسید: ${order.receiptNumber ?? "-"}',
                style: theme.textTheme.titleMedium,
              ),
              Text(
                'تاریخ: ${_formatPersianDate(order.orderDate)}',
                style: theme.textTheme.titleMedium,
              ),
            ],
          ),
        ),
        const Divider(thickness: 1, height: 32),
      ],
    );
  }

  /// ساخت بخش اطلاعات مشتری
  static Widget _buildCustomerInfo(Customer? customer, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: theme.colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'اطلاعات مشتری',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _buildInfoItem(
                  theme,
                  'نام:',
                  customer?.name ?? "-",
                  Icons.badge_outlined,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  theme,
                  'شماره تماس:',
                  customer?.phoneNumber ?? "-",
                  Icons.phone_outlined,
                ),
              ),
            ],
          ),
          if (customer?.address != null && customer!.address!.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildInfoItem(
              theme,
              'آدرس:',
              customer.address!,
              Icons.location_on_outlined,
            ),
          ],
        ],
      ),
    );
  }

  /// ساخت بخش جزئیات سفارش
  static Widget _buildOrderDetails(
      Order order, NumberFormat currencyFormat, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long,
                  color: theme.colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'مشخصات سفارش',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'قیمت:',
                  currencyFormat.format(order.price),
                  Icons.payments_outlined,
                ),
              ),
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'وضعیت پرداخت:',
                  order.isPaid ? 'پرداخت شده' : 'پرداخت نشده',
                  Icons.payment,
                  valueColor: order.isPaid
                      ? theme.colorScheme.primary
                      : theme.colorScheme.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'تاریخ سفارش:',
                  _formatPersianDate(order.orderDate),
                  Icons.calendar_today_outlined,
                ),
              ),
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'تاریخ تحویل:',
                  order.deliveryDate != null
                      ? _formatPersianDate(order.deliveryDate!)
                      : 'تعیین نشده',
                  Icons.event_available_outlined,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildDetailItem(
            theme,
            'وضعیت سفارش:',
            _getStatusText(order.status),
            Icons.hourglass_bottom_outlined,
            valueColor: _getStatusColor(order.status, theme),
          ),
          if (order.notes != null && order.notes!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'یادداشت‌ها:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.3)),
              ),
              child: Text(
                order.notes!,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// ساخت بخش اندازه‌گیری‌ها
  static Widget _buildMeasurements(Measurement measurement, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.straighten,
                  color: theme.colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'اندازه‌گیری‌ها',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'قد:',
                  '${measurement.height} سانتی‌متر',
                  Icons.height,
                ),
              ),
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'شانه:',
                  '${measurement.shoulderWidth} سانتی‌متر',
                  Icons.square_foot,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'آستین:',
                  '${measurement.sleeveLength} سانتی‌متر (${_getSleeveTypeText(measurement.sleeveType)})',
                  Icons.design_services,
                ),
              ),
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'یخن:',
                  '${measurement.neckCircumference} سانتی‌متر (${_getCollarTypeText(measurement.collarType)})',
                  Icons.circle_outlined,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'بغل:',
                  '${measurement.sideWidth} سانتی‌متر',
                  Icons.width_normal,
                ),
              ),
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'دامن:',
                  '${measurement.skirtLength} سانتی‌متر (${_getSkirtTypeText(measurement.skirtType)})',
                  Icons.format_line_spacing,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'تنبان:',
                  '${measurement.trouserLength} سانتی‌متر (${_getTrouserTypeText(measurement.trouserType)})',
                  Icons.format_align_justify,
                ),
              ),
              Expanded(
                child: _buildMeasurementItem(
                  theme,
                  'پاچه:',
                  '${measurement.hemWidth} سانتی‌متر',
                  Icons.format_indent_decrease,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildMeasurementItem(
            theme,
            'جیب تنبان:',
            measurement.hasTrouserPocket ? 'دارد' : 'ندارد',
            Icons.wallet,
          ),
        ],
      ),
    );
  }

  /// ساخت بخش پاورقی
  static Widget _buildFooter(ThemeData theme) {
    return Column(
      children: [
        const Divider(thickness: 1),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.design_services_rounded,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'دوخت‌یار - سیستم مدیریت خیاطی',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'با تشکر از اعتماد شما',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// ساخت آیتم اطلاعات
  static Widget _buildInfoItem(
      ThemeData theme, String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// ساخت آیتم جزئیات
  static Widget _buildDetailItem(
    ThemeData theme,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: valueColor,
                  fontWeight: valueColor != null ? FontWeight.bold : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// ساخت آیتم اندازه‌گیری
  static Widget _buildMeasurementItem(
      ThemeData theme, String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// تبدیل تاریخ میلادی به شمسی
  static String _formatPersianDate(DateTime date) {
    final jalali = Jalali.fromDateTime(date);
    return '${jalali.year}/${jalali.month.toString().padLeft(2, '0')}/${jalali.day.toString().padLeft(2, '0')}';
  }

  /// متن وضعیت سفارش
  static String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'در انتظار';
      case OrderStatus.inProgress:
        return 'در حال انجام';
      case OrderStatus.completed:
        return 'تکمیل شده';
      case OrderStatus.delivered:
        return 'تحویل داده شده';
      case OrderStatus.cancelled:
        return 'لغو شده';
    }
  }

  /// رنگ وضعیت سفارش
  static Color _getStatusColor(OrderStatus status, ThemeData theme) {
    switch (status) {
      case OrderStatus.pending:
        return theme.colorScheme.tertiary;
      case OrderStatus.inProgress:
        return theme.colorScheme.secondary;
      case OrderStatus.completed:
        return theme.colorScheme.primary;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return theme.colorScheme.error;
    }
  }

  /// متن نوع آستین
  static String _getSleeveTypeText(SleeveType type) {
    switch (type) {
      case SleeveType.cuffed:
        return 'کفدار';
      case SleeveType.cufflinks:
        return 'کفلینکس';
      case SleeveType.plain:
        return 'ساده';
      case SleeveType.banded:
        return 'بندکدار';
      case SleeveType.boat:
        return 'کشتی';
      case SleeveType.stitchedEdge:
        return 'لب پخته';
    }
  }

  /// متن نوع یقه
  static String _getCollarTypeText(CollarType type) {
    switch (type) {
      case CollarType.hindi:
        return 'هندی';
      case CollarType.pakistani:
        return 'پاکستانی';
      case CollarType.half:
        return 'نیمه';
      case CollarType.reverse:
        return 'چپه';
      case CollarType.qasemi:
        return 'قاسمی';
    }
  }

  /// متن نوع دامن
  static String _getSkirtTypeText(SkirtType type) {
    switch (type) {
      case SkirtType.punjabi:
        return 'پنجابی';
      case SkirtType.round:
        return 'گرد';
    }
  }

  /// متن نوع شلوار
  static String _getTrouserTypeText(TrouserType type) {
    switch (type) {
      case TrouserType.regular:
        return 'معمولی';
      case TrouserType.wide:
        return 'گشاد';
      case TrouserType.tight:
        return 'چسپ';
    }
  }
}
