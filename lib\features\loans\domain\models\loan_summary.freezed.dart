// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'loan_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoanSummary _$LoanSummaryFromJson(Map<String, dynamic> json) {
  return _LoanSummary.fromJson(json);
}

/// @nodoc
mixin _$LoanSummary {
  int get totalLoans => throw _privateConstructorUsedError;
  int get activeLoans => throw _privateConstructorUsedError;
  int get overdueLoans => throw _privateConstructorUsedError;
  int get completedLoans => throw _privateConstructorUsedError;
  double get totalLoanAmount => throw _privateConstructorUsedError;
  double get totalPaidAmount => throw _privateConstructorUsedError;
  double get totalOutstandingAmount => throw _privateConstructorUsedError;
  double get totalOverdueAmount => throw _privateConstructorUsedError;

  /// Serializes this LoanSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoanSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoanSummaryCopyWith<LoanSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoanSummaryCopyWith<$Res> {
  factory $LoanSummaryCopyWith(
          LoanSummary value, $Res Function(LoanSummary) then) =
      _$LoanSummaryCopyWithImpl<$Res, LoanSummary>;
  @useResult
  $Res call(
      {int totalLoans,
      int activeLoans,
      int overdueLoans,
      int completedLoans,
      double totalLoanAmount,
      double totalPaidAmount,
      double totalOutstandingAmount,
      double totalOverdueAmount});
}

/// @nodoc
class _$LoanSummaryCopyWithImpl<$Res, $Val extends LoanSummary>
    implements $LoanSummaryCopyWith<$Res> {
  _$LoanSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoanSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalLoans = null,
    Object? activeLoans = null,
    Object? overdueLoans = null,
    Object? completedLoans = null,
    Object? totalLoanAmount = null,
    Object? totalPaidAmount = null,
    Object? totalOutstandingAmount = null,
    Object? totalOverdueAmount = null,
  }) {
    return _then(_value.copyWith(
      totalLoans: null == totalLoans
          ? _value.totalLoans
          : totalLoans // ignore: cast_nullable_to_non_nullable
              as int,
      activeLoans: null == activeLoans
          ? _value.activeLoans
          : activeLoans // ignore: cast_nullable_to_non_nullable
              as int,
      overdueLoans: null == overdueLoans
          ? _value.overdueLoans
          : overdueLoans // ignore: cast_nullable_to_non_nullable
              as int,
      completedLoans: null == completedLoans
          ? _value.completedLoans
          : completedLoans // ignore: cast_nullable_to_non_nullable
              as int,
      totalLoanAmount: null == totalLoanAmount
          ? _value.totalLoanAmount
          : totalLoanAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalPaidAmount: null == totalPaidAmount
          ? _value.totalPaidAmount
          : totalPaidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalOutstandingAmount: null == totalOutstandingAmount
          ? _value.totalOutstandingAmount
          : totalOutstandingAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalOverdueAmount: null == totalOverdueAmount
          ? _value.totalOverdueAmount
          : totalOverdueAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoanSummaryImplCopyWith<$Res>
    implements $LoanSummaryCopyWith<$Res> {
  factory _$$LoanSummaryImplCopyWith(
          _$LoanSummaryImpl value, $Res Function(_$LoanSummaryImpl) then) =
      __$$LoanSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalLoans,
      int activeLoans,
      int overdueLoans,
      int completedLoans,
      double totalLoanAmount,
      double totalPaidAmount,
      double totalOutstandingAmount,
      double totalOverdueAmount});
}

/// @nodoc
class __$$LoanSummaryImplCopyWithImpl<$Res>
    extends _$LoanSummaryCopyWithImpl<$Res, _$LoanSummaryImpl>
    implements _$$LoanSummaryImplCopyWith<$Res> {
  __$$LoanSummaryImplCopyWithImpl(
      _$LoanSummaryImpl _value, $Res Function(_$LoanSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoanSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalLoans = null,
    Object? activeLoans = null,
    Object? overdueLoans = null,
    Object? completedLoans = null,
    Object? totalLoanAmount = null,
    Object? totalPaidAmount = null,
    Object? totalOutstandingAmount = null,
    Object? totalOverdueAmount = null,
  }) {
    return _then(_$LoanSummaryImpl(
      totalLoans: null == totalLoans
          ? _value.totalLoans
          : totalLoans // ignore: cast_nullable_to_non_nullable
              as int,
      activeLoans: null == activeLoans
          ? _value.activeLoans
          : activeLoans // ignore: cast_nullable_to_non_nullable
              as int,
      overdueLoans: null == overdueLoans
          ? _value.overdueLoans
          : overdueLoans // ignore: cast_nullable_to_non_nullable
              as int,
      completedLoans: null == completedLoans
          ? _value.completedLoans
          : completedLoans // ignore: cast_nullable_to_non_nullable
              as int,
      totalLoanAmount: null == totalLoanAmount
          ? _value.totalLoanAmount
          : totalLoanAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalPaidAmount: null == totalPaidAmount
          ? _value.totalPaidAmount
          : totalPaidAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalOutstandingAmount: null == totalOutstandingAmount
          ? _value.totalOutstandingAmount
          : totalOutstandingAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalOverdueAmount: null == totalOverdueAmount
          ? _value.totalOverdueAmount
          : totalOverdueAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoanSummaryImpl implements _LoanSummary {
  const _$LoanSummaryImpl(
      {this.totalLoans = 0,
      this.activeLoans = 0,
      this.overdueLoans = 0,
      this.completedLoans = 0,
      this.totalLoanAmount = 0.0,
      this.totalPaidAmount = 0.0,
      this.totalOutstandingAmount = 0.0,
      this.totalOverdueAmount = 0.0});

  factory _$LoanSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoanSummaryImplFromJson(json);

  @override
  @JsonKey()
  final int totalLoans;
  @override
  @JsonKey()
  final int activeLoans;
  @override
  @JsonKey()
  final int overdueLoans;
  @override
  @JsonKey()
  final int completedLoans;
  @override
  @JsonKey()
  final double totalLoanAmount;
  @override
  @JsonKey()
  final double totalPaidAmount;
  @override
  @JsonKey()
  final double totalOutstandingAmount;
  @override
  @JsonKey()
  final double totalOverdueAmount;

  @override
  String toString() {
    return 'LoanSummary(totalLoans: $totalLoans, activeLoans: $activeLoans, overdueLoans: $overdueLoans, completedLoans: $completedLoans, totalLoanAmount: $totalLoanAmount, totalPaidAmount: $totalPaidAmount, totalOutstandingAmount: $totalOutstandingAmount, totalOverdueAmount: $totalOverdueAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoanSummaryImpl &&
            (identical(other.totalLoans, totalLoans) ||
                other.totalLoans == totalLoans) &&
            (identical(other.activeLoans, activeLoans) ||
                other.activeLoans == activeLoans) &&
            (identical(other.overdueLoans, overdueLoans) ||
                other.overdueLoans == overdueLoans) &&
            (identical(other.completedLoans, completedLoans) ||
                other.completedLoans == completedLoans) &&
            (identical(other.totalLoanAmount, totalLoanAmount) ||
                other.totalLoanAmount == totalLoanAmount) &&
            (identical(other.totalPaidAmount, totalPaidAmount) ||
                other.totalPaidAmount == totalPaidAmount) &&
            (identical(other.totalOutstandingAmount, totalOutstandingAmount) ||
                other.totalOutstandingAmount == totalOutstandingAmount) &&
            (identical(other.totalOverdueAmount, totalOverdueAmount) ||
                other.totalOverdueAmount == totalOverdueAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalLoans,
      activeLoans,
      overdueLoans,
      completedLoans,
      totalLoanAmount,
      totalPaidAmount,
      totalOutstandingAmount,
      totalOverdueAmount);

  /// Create a copy of LoanSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoanSummaryImplCopyWith<_$LoanSummaryImpl> get copyWith =>
      __$$LoanSummaryImplCopyWithImpl<_$LoanSummaryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoanSummaryImplToJson(
      this,
    );
  }
}

abstract class _LoanSummary implements LoanSummary {
  const factory _LoanSummary(
      {final int totalLoans,
      final int activeLoans,
      final int overdueLoans,
      final int completedLoans,
      final double totalLoanAmount,
      final double totalPaidAmount,
      final double totalOutstandingAmount,
      final double totalOverdueAmount}) = _$LoanSummaryImpl;

  factory _LoanSummary.fromJson(Map<String, dynamic> json) =
      _$LoanSummaryImpl.fromJson;

  @override
  int get totalLoans;
  @override
  int get activeLoans;
  @override
  int get overdueLoans;
  @override
  int get completedLoans;
  @override
  double get totalLoanAmount;
  @override
  double get totalPaidAmount;
  @override
  double get totalOutstandingAmount;
  @override
  double get totalOverdueAmount;

  /// Create a copy of LoanSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoanSummaryImplCopyWith<_$LoanSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
