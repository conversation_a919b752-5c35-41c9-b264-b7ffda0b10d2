import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../customers/domain/models/customer.dart';
import 'measurement.dart';

part 'order.freezed.dart';
part 'order.g.dart';

enum OrderStatus {
  pending,
  inProgress,
  completed,
  delivered,
  cancelled,
}

@freezed
class Order with _$Order {
  const Order._();

  const factory Order({
    required String id,
    required String customerId,
    String? receiptNumber,
    required double price,
    required DateTime orderDate,
    DateTime? deliveryDate,
    @Default(OrderStatus.pending) OrderStatus status,
    String? notes,
    @Default(false) bool isPaid,
    Customer? customer,
    Measurement? measurement,
  }) = _Order;

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  
  // Getter for date (returns orderDate)
  DateTime get date => orderDate;
  
  // Getter for totalPrice (returns price)
  double get totalPrice => price;
  
  // Getter for items (returns empty list for now)
  List<dynamic> get items => [];
} 