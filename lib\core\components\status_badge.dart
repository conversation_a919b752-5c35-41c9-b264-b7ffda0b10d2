import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/constants.dart';
import '../theme/text_styles.dart';

class StatusBadge extends StatelessWidget {
  final String text;
  final Color color;
  final Color? backgroundColor;
  final IconData? icon;
  final double? iconSize;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const StatusBadge({
    super.key,
    required this.text,
    required this.color,
    this.backgroundColor,
    this.icon,
    this.iconSize = 16,
    this.padding,
    this.borderRadius,
  });

  factory StatusBadge.success({
    required String text,
    IconData? icon,
    double? iconSize,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
  }) {
    return StatusBadge(
      text: text,
      color: AppColors.success,
      backgroundColor: AppColors.successContainer,
      icon: icon,
      iconSize: iconSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  factory StatusBadge.warning({
    required String text,
    IconData? icon,
    double? iconSize,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
  }) {
    return StatusBadge(
      text: text,
      color: AppColors.warning,
      backgroundColor: AppColors.warningContainer,
      icon: icon,
      iconSize: iconSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  factory StatusBadge.error({
    required String text,
    IconData? icon,
    double? iconSize,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
  }) {
    return StatusBadge(
      text: text,
      color: AppColors.error,
      backgroundColor: AppColors.errorContainer,
      icon: icon,
      iconSize: iconSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  factory StatusBadge.primary({
    required String text,
    IconData? icon,
    double? iconSize,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
  }) {
    return StatusBadge(
      text: text,
      color: AppColors.primary,
      backgroundColor: AppColors.primaryContainer,
      icon: icon,
      iconSize: iconSize,
      padding: padding,
      borderRadius: borderRadius,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ??
          const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
      decoration: BoxDecoration(
        color: backgroundColor ?? color.withOpacity(0.1),
        borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.lg),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        textDirection: TextDirection.rtl,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: iconSize,
              color: color,
            ),
            AppSpacing.gapXS,
          ],
          Text(
            text,
            style: AppTextStyles.labelSmall.copyWith(color: color),
          ),
        ],
      ),
    );
  }
} 