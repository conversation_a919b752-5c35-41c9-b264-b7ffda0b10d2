// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'measurement.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Measurement _$MeasurementFromJson(Map<String, dynamic> json) {
  return _Measurement.fromJson(json);
}

/// @nodoc
mixin _$Measurement {
  double get height => throw _privateConstructorUsedError;
  double get sleeveLength => throw _privateConstructorUsedError;
  SleeveType get sleeveType => throw _privateConstructorUsedError;
  double get shoulderWidth => throw _privateConstructorUsedError;
  double get neckCircumference => throw _privateConstructorUsedError;
  CollarType get collarType => throw _privateConstructorUsedError;
  double get sideWidth => throw _privateConstructorUsedError;
  double get skirtLength => throw _privateConstructorUsedError;
  SkirtType get skirtType => throw _privateConstructorUsedError;
  double get trouserLength => throw _privateConstructorUsedError;
  TrouserType get trouserType => throw _privateConstructorUsedError;
  double get hemWidth => throw _privateConstructorUsedError;
  bool get hasTrouserPocket => throw _privateConstructorUsedError;

  /// Serializes this Measurement to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MeasurementCopyWith<Measurement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MeasurementCopyWith<$Res> {
  factory $MeasurementCopyWith(
          Measurement value, $Res Function(Measurement) then) =
      _$MeasurementCopyWithImpl<$Res, Measurement>;
  @useResult
  $Res call(
      {double height,
      double sleeveLength,
      SleeveType sleeveType,
      double shoulderWidth,
      double neckCircumference,
      CollarType collarType,
      double sideWidth,
      double skirtLength,
      SkirtType skirtType,
      double trouserLength,
      TrouserType trouserType,
      double hemWidth,
      bool hasTrouserPocket});
}

/// @nodoc
class _$MeasurementCopyWithImpl<$Res, $Val extends Measurement>
    implements $MeasurementCopyWith<$Res> {
  _$MeasurementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = null,
    Object? sleeveLength = null,
    Object? sleeveType = null,
    Object? shoulderWidth = null,
    Object? neckCircumference = null,
    Object? collarType = null,
    Object? sideWidth = null,
    Object? skirtLength = null,
    Object? skirtType = null,
    Object? trouserLength = null,
    Object? trouserType = null,
    Object? hemWidth = null,
    Object? hasTrouserPocket = null,
  }) {
    return _then(_value.copyWith(
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      sleeveLength: null == sleeveLength
          ? _value.sleeveLength
          : sleeveLength // ignore: cast_nullable_to_non_nullable
              as double,
      sleeveType: null == sleeveType
          ? _value.sleeveType
          : sleeveType // ignore: cast_nullable_to_non_nullable
              as SleeveType,
      shoulderWidth: null == shoulderWidth
          ? _value.shoulderWidth
          : shoulderWidth // ignore: cast_nullable_to_non_nullable
              as double,
      neckCircumference: null == neckCircumference
          ? _value.neckCircumference
          : neckCircumference // ignore: cast_nullable_to_non_nullable
              as double,
      collarType: null == collarType
          ? _value.collarType
          : collarType // ignore: cast_nullable_to_non_nullable
              as CollarType,
      sideWidth: null == sideWidth
          ? _value.sideWidth
          : sideWidth // ignore: cast_nullable_to_non_nullable
              as double,
      skirtLength: null == skirtLength
          ? _value.skirtLength
          : skirtLength // ignore: cast_nullable_to_non_nullable
              as double,
      skirtType: null == skirtType
          ? _value.skirtType
          : skirtType // ignore: cast_nullable_to_non_nullable
              as SkirtType,
      trouserLength: null == trouserLength
          ? _value.trouserLength
          : trouserLength // ignore: cast_nullable_to_non_nullable
              as double,
      trouserType: null == trouserType
          ? _value.trouserType
          : trouserType // ignore: cast_nullable_to_non_nullable
              as TrouserType,
      hemWidth: null == hemWidth
          ? _value.hemWidth
          : hemWidth // ignore: cast_nullable_to_non_nullable
              as double,
      hasTrouserPocket: null == hasTrouserPocket
          ? _value.hasTrouserPocket
          : hasTrouserPocket // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MeasurementImplCopyWith<$Res>
    implements $MeasurementCopyWith<$Res> {
  factory _$$MeasurementImplCopyWith(
          _$MeasurementImpl value, $Res Function(_$MeasurementImpl) then) =
      __$$MeasurementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double height,
      double sleeveLength,
      SleeveType sleeveType,
      double shoulderWidth,
      double neckCircumference,
      CollarType collarType,
      double sideWidth,
      double skirtLength,
      SkirtType skirtType,
      double trouserLength,
      TrouserType trouserType,
      double hemWidth,
      bool hasTrouserPocket});
}

/// @nodoc
class __$$MeasurementImplCopyWithImpl<$Res>
    extends _$MeasurementCopyWithImpl<$Res, _$MeasurementImpl>
    implements _$$MeasurementImplCopyWith<$Res> {
  __$$MeasurementImplCopyWithImpl(
      _$MeasurementImpl _value, $Res Function(_$MeasurementImpl) _then)
      : super(_value, _then);

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = null,
    Object? sleeveLength = null,
    Object? sleeveType = null,
    Object? shoulderWidth = null,
    Object? neckCircumference = null,
    Object? collarType = null,
    Object? sideWidth = null,
    Object? skirtLength = null,
    Object? skirtType = null,
    Object? trouserLength = null,
    Object? trouserType = null,
    Object? hemWidth = null,
    Object? hasTrouserPocket = null,
  }) {
    return _then(_$MeasurementImpl(
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      sleeveLength: null == sleeveLength
          ? _value.sleeveLength
          : sleeveLength // ignore: cast_nullable_to_non_nullable
              as double,
      sleeveType: null == sleeveType
          ? _value.sleeveType
          : sleeveType // ignore: cast_nullable_to_non_nullable
              as SleeveType,
      shoulderWidth: null == shoulderWidth
          ? _value.shoulderWidth
          : shoulderWidth // ignore: cast_nullable_to_non_nullable
              as double,
      neckCircumference: null == neckCircumference
          ? _value.neckCircumference
          : neckCircumference // ignore: cast_nullable_to_non_nullable
              as double,
      collarType: null == collarType
          ? _value.collarType
          : collarType // ignore: cast_nullable_to_non_nullable
              as CollarType,
      sideWidth: null == sideWidth
          ? _value.sideWidth
          : sideWidth // ignore: cast_nullable_to_non_nullable
              as double,
      skirtLength: null == skirtLength
          ? _value.skirtLength
          : skirtLength // ignore: cast_nullable_to_non_nullable
              as double,
      skirtType: null == skirtType
          ? _value.skirtType
          : skirtType // ignore: cast_nullable_to_non_nullable
              as SkirtType,
      trouserLength: null == trouserLength
          ? _value.trouserLength
          : trouserLength // ignore: cast_nullable_to_non_nullable
              as double,
      trouserType: null == trouserType
          ? _value.trouserType
          : trouserType // ignore: cast_nullable_to_non_nullable
              as TrouserType,
      hemWidth: null == hemWidth
          ? _value.hemWidth
          : hemWidth // ignore: cast_nullable_to_non_nullable
              as double,
      hasTrouserPocket: null == hasTrouserPocket
          ? _value.hasTrouserPocket
          : hasTrouserPocket // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MeasurementImpl implements _Measurement {
  const _$MeasurementImpl(
      {required this.height,
      required this.sleeveLength,
      required this.sleeveType,
      required this.shoulderWidth,
      required this.neckCircumference,
      required this.collarType,
      required this.sideWidth,
      required this.skirtLength,
      required this.skirtType,
      required this.trouserLength,
      required this.trouserType,
      required this.hemWidth,
      this.hasTrouserPocket = false});

  factory _$MeasurementImpl.fromJson(Map<String, dynamic> json) =>
      _$$MeasurementImplFromJson(json);

  @override
  final double height;
  @override
  final double sleeveLength;
  @override
  final SleeveType sleeveType;
  @override
  final double shoulderWidth;
  @override
  final double neckCircumference;
  @override
  final CollarType collarType;
  @override
  final double sideWidth;
  @override
  final double skirtLength;
  @override
  final SkirtType skirtType;
  @override
  final double trouserLength;
  @override
  final TrouserType trouserType;
  @override
  final double hemWidth;
  @override
  @JsonKey()
  final bool hasTrouserPocket;

  @override
  String toString() {
    return 'Measurement(height: $height, sleeveLength: $sleeveLength, sleeveType: $sleeveType, shoulderWidth: $shoulderWidth, neckCircumference: $neckCircumference, collarType: $collarType, sideWidth: $sideWidth, skirtLength: $skirtLength, skirtType: $skirtType, trouserLength: $trouserLength, trouserType: $trouserType, hemWidth: $hemWidth, hasTrouserPocket: $hasTrouserPocket)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MeasurementImpl &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.sleeveLength, sleeveLength) ||
                other.sleeveLength == sleeveLength) &&
            (identical(other.sleeveType, sleeveType) ||
                other.sleeveType == sleeveType) &&
            (identical(other.shoulderWidth, shoulderWidth) ||
                other.shoulderWidth == shoulderWidth) &&
            (identical(other.neckCircumference, neckCircumference) ||
                other.neckCircumference == neckCircumference) &&
            (identical(other.collarType, collarType) ||
                other.collarType == collarType) &&
            (identical(other.sideWidth, sideWidth) ||
                other.sideWidth == sideWidth) &&
            (identical(other.skirtLength, skirtLength) ||
                other.skirtLength == skirtLength) &&
            (identical(other.skirtType, skirtType) ||
                other.skirtType == skirtType) &&
            (identical(other.trouserLength, trouserLength) ||
                other.trouserLength == trouserLength) &&
            (identical(other.trouserType, trouserType) ||
                other.trouserType == trouserType) &&
            (identical(other.hemWidth, hemWidth) ||
                other.hemWidth == hemWidth) &&
            (identical(other.hasTrouserPocket, hasTrouserPocket) ||
                other.hasTrouserPocket == hasTrouserPocket));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      height,
      sleeveLength,
      sleeveType,
      shoulderWidth,
      neckCircumference,
      collarType,
      sideWidth,
      skirtLength,
      skirtType,
      trouserLength,
      trouserType,
      hemWidth,
      hasTrouserPocket);

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MeasurementImplCopyWith<_$MeasurementImpl> get copyWith =>
      __$$MeasurementImplCopyWithImpl<_$MeasurementImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MeasurementImplToJson(
      this,
    );
  }
}

abstract class _Measurement implements Measurement {
  const factory _Measurement(
      {required final double height,
      required final double sleeveLength,
      required final SleeveType sleeveType,
      required final double shoulderWidth,
      required final double neckCircumference,
      required final CollarType collarType,
      required final double sideWidth,
      required final double skirtLength,
      required final SkirtType skirtType,
      required final double trouserLength,
      required final TrouserType trouserType,
      required final double hemWidth,
      final bool hasTrouserPocket}) = _$MeasurementImpl;

  factory _Measurement.fromJson(Map<String, dynamic> json) =
      _$MeasurementImpl.fromJson;

  @override
  double get height;
  @override
  double get sleeveLength;
  @override
  SleeveType get sleeveType;
  @override
  double get shoulderWidth;
  @override
  double get neckCircumference;
  @override
  CollarType get collarType;
  @override
  double get sideWidth;
  @override
  double get skirtLength;
  @override
  SkirtType get skirtType;
  @override
  double get trouserLength;
  @override
  TrouserType get trouserType;
  @override
  double get hemWidth;
  @override
  bool get hasTrouserPocket;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MeasurementImplCopyWith<_$MeasurementImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
