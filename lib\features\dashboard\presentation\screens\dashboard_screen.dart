import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../auth/providers/auth_provider.dart';
import '../../../../core/theme/theme_provider.dart';
import '../widgets/kpi_card.dart';
import '../../../orders/presentation/screens/orders_screen.dart';
import '../../../orders/presentation/screens/add_order_screen.dart';
import '../../../customers/presentation/screens/customers_screen.dart';
import '../../../customers/presentation/screens/add_customer_screen.dart';
import '../../../customers/providers/customers_provider.dart';
import '../../../orders/providers/orders_provider.dart';
import '../../../orders/domain/models/order.dart';
// import '../../../settings/presentation/screens/settings_screen.dart';
import '../../../settings/presentation/screens/help_screen.dart';
import '../../../settings/presentation/screens/about_screen.dart';
import '../../../auth/presentation/screens/edit_business_profile_screen.dart';
import '../../../auth/presentation/screens/change_password_screen.dart';
// import '../../../auth/providers/business_profile_provider.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _scrollController.addListener(_onScroll);
    _animationController.forward();
  }
  
  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen(authProvider, (previous, next) {
      next.whenData((state) {
        final (user, profile) = state;
        if (user == null && mounted) {
          Navigator.of(context).pushReplacementNamed('/auth');
        }
      });
    });

    return authState.when(
      data: (data) {
        final (_, profile) = data;
        if (profile == null) return const SizedBox.shrink();

        return Directionality(
          textDirection: ui.TextDirection.rtl,
          child: Scaffold(
            appBar: _buildAppBar(profile),
            body: FadeTransition(
              opacity: _fadeAnimation,
              child: IndexedStack(
              index: _selectedIndex,
              children: [
                _buildHomeTab(profile),
                const OrdersScreen(),
                const CustomersScreen(),
                _buildSettingsTab(),
              ],
            ),
            ),
            floatingActionButton: _selectedIndex == 0 || _selectedIndex == 1 || _selectedIndex == 2
                ? AnimatedSlide(
                    duration: const Duration(milliseconds: 200),
                    offset: const Offset(0, 0),
                    child: _buildAnimatedFAB(),
                  )
                : null,
            bottomNavigationBar: _buildBottomNavigationBar(),
          ),
        );
      },
      loading: () => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'در حال بارگذاری...',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Text(error.toString()),
        ),
      ),
    );
  }
  
  PreferredSizeWidget _buildAppBar(profile) {
    final theme = Theme.of(context);
    final themeNotifier = ref.read(themeProvider.notifier);
    // final isDarkMode = ref.watch(isDarkModeProvider); // Remove this line as provider is deleted
    
    return AppBar(
      toolbarHeight: 70,
      centerTitle: false,
      elevation: 0,
      scrolledUnderElevation: _isScrolled ? 3 : 0,
      shadowColor: theme.colorScheme.shadow.withOpacity(0.5),
      backgroundColor: _isScrolled
          ? theme.colorScheme.surface.withOpacity(0.8)
          : Colors.transparent,
      flexibleSpace: ClipRect(
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(
            sigmaX: _isScrolled ? 8 : 0,
            sigmaY: _isScrolled ? 8 : 0
          ),
          child: Container(
            decoration: BoxDecoration(
              gradient: _isScrolled
                  ? LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        theme.colorScheme.surface.withOpacity(0.85),
                        theme.colorScheme.surface.withOpacity(0.9),
                      ],
                    )
                  : null,
            ),
          ),
        ),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.15),
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.content_cut,
              color: theme.colorScheme.primary,
              size: 22,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'دوخت‌یار',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              Text(
                profile.businessName,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        // Remove the theme toggle IconButton completely
        // IconButton(
        //   icon: AnimatedSwitcher(...),
        //   onPressed: () {
        //     // themeNotifier.toggleTheme(); // Remove call to deleted method
        //   },
        // ),
        PopupMenuButton(
          icon: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.15),
              radius: 16,
              child: Text(
                profile.businessName.isNotEmpty ? profile.businessName[0].toUpperCase() : 'U',
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          offset: const Offset(0, 56),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          itemBuilder: (context) => <PopupMenuEntry<String>>[
            PopupMenuItem<String>(
              value: 'profile',
              child: Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text('پروفایل کاربری'),
                ],
              ),
              onTap: () {
                 Navigator.push(context, MaterialPageRoute(builder: (context) => EditBusinessProfileScreen(profile: profile)));
              },
            ),
            PopupMenuItem<String>(
              value: 'settings',
              child: Row(
                children: [
                  Icon(
                    Icons.settings_outlined,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text('تنظیمات'),
                ],
              ),
              onTap: () {
                setState(() => _selectedIndex = 3);
              },
            ),
             PopupMenuItem<String>(
              value: 'change_password',
              child: Row(
                children: [
                  Icon(
                    Icons.lock_outline,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text('تغییر رمز عبور'),
                ],
              ),
              onTap: () {
                 Navigator.push(context, MaterialPageRoute(builder: (context) => const ChangePasswordScreen()));
              },
            ),
            const PopupMenuDivider(), // This is already PopupMenuEntry
            PopupMenuItem<String>(
              value: 'help',
              child: Row(
                children: [
                  Icon(
                    Icons.help_outline_rounded,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text('راهنما'),
                ],
              ),
              onTap: () {
                 Navigator.push(context, MaterialPageRoute(builder: (context) => const HelpScreen()));
              },
            ),
            PopupMenuItem<String>(
              value: 'about',
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text('درباره'),
                ],
              ),
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const AboutScreen()));
              },
            ),
            const PopupMenuDivider(), // This is already PopupMenuEntry
            PopupMenuItem<String>(
              value: 'logout',
              child: Row(
                children: [
                  Icon(
                    Icons.logout,
                    color: theme.colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'خروج از حساب',
                    style: TextStyle(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ),
              onTap: () {
                Future.delayed(
                  const Duration(milliseconds: 200),
                  () => showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('خروج از حساب کاربری'),
                      content: const Text('آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('انصراف'),
                        ),
                        FilledButton(
                          onPressed: () {
                            Navigator.pop(context);
                            ref.read(authProvider.notifier).signOut();
                          },
                          style: FilledButton.styleFrom(
                            backgroundColor: theme.colorScheme.error,
                          ),
                          child: const Text('خروج'),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        const SizedBox(width: 12),
      ],
    );
  }
  
  Widget _buildBottomNavigationBar() {
    final theme = Theme.of(context);
    
    return NavigationBar(
      selectedIndex: _selectedIndex,
      onDestinationSelected: (index) {
        setState(() => _selectedIndex = index);
      },
      elevation: 8,
      shadowColor: theme.colorScheme.shadow,
      backgroundColor: theme.colorScheme.surface,
      indicatorColor: theme.colorScheme.primary.withOpacity(0.15),
      labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
      animationDuration: const Duration(milliseconds: 300),
      destinations: [
        NavigationDestination(
          icon: const Icon(Icons.home_outlined),
          selectedIcon: Icon(
            Icons.home_rounded,
            color: theme.colorScheme.primary,
          ),
          label: 'خانه',
        ),
        NavigationDestination(
          icon: const Icon(Icons.receipt_outlined),
          selectedIcon: Icon(
            Icons.receipt_rounded,
            color: theme.colorScheme.primary,
          ),
          label: 'سفارشات',
        ),
        NavigationDestination(
          icon: const Icon(Icons.people_outline),
          selectedIcon: Icon(
            Icons.people_rounded,
            color: theme.colorScheme.primary,
          ),
          label: 'مشتریان',
        ),
        NavigationDestination(
          icon: const Icon(Icons.settings_outlined),
          selectedIcon: Icon(
            Icons.settings_rounded,
            color: theme.colorScheme.primary,
          ),
          label: 'تنظیمات',
        ),
      ],
    );
  }

  Widget _buildHomeTab(profile) {
    final theme = Theme.of(context);
    final customersAsyncValue = ref.watch(customersProvider);
    final ordersAsyncValue = ref.watch(ordersProvider);
    
    final currencyFormat = NumberFormat.currency(
      locale: 'fa',
      symbol: 'افغانی',
      decimalDigits: 0,
      customPattern: '#,##0 ¤',
    );

    // Number format for regular integers using Persian numerals
    final numberFormat = NumberFormat('#,##0', 'fa');

    final totalCustomers = customersAsyncValue.when(
      data: (customers) => customers.length,
      loading: () => -1,
      error: (_, __) => -1,
    );
    
    final activeOrders = ordersAsyncValue.when(
      data: (orders) => orders.where((order) => order.status != OrderStatus.delivered && order.status != OrderStatus.cancelled).length,
      loading: () => -1,
      error: (_, __) => -1,
    );

    final totalRevenue = ordersAsyncValue.when(
      data: (orders) {
        return orders.fold<double>(0.0, (sum, order) => sum + order.price);
      },
      loading: () => -1.0,
      error: (_, __) => -1.0,
    );

    return ListView(
      controller: _scrollController,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 20),
      children: [
        Row(
          children: [
            Expanded(
              child: Center(
                child: Text(
                  'خلاصه وضعیت',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            IconButton.filledTonal(
              icon: const Icon(Icons.refresh_rounded),
              tooltip: 'بارگذاری مجدد داده‌ها',
              onPressed: () {
                // TODO: Refresh data
              },
            ),
          ],
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: _buildModernKpiCard(
                title: 'مشتریان',
                value: totalCustomers >= 0 ? numberFormat.format(totalCustomers) : '...',
                icon: Icons.people_rounded,
                subtitle: 'کل مشتریان',
                onTap: () {
                  setState(() => _selectedIndex = 2);
                },
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary.withOpacity(0.7),
                    theme.colorScheme.primary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernKpiCard(
                title: 'سفارشات فعال',
                value: activeOrders >= 0 ? numberFormat.format(activeOrders) : '...',
                icon: Icons.receipt_rounded,
                subtitle: 'در حال انجام',
                onTap: () {
                  setState(() => _selectedIndex = 1);
                },
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.secondary.withOpacity(0.7),
                    theme.colorScheme.secondary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildModernKpiCard(
                title: 'درآمد کل',
                value: totalRevenue >= 0
                    ? currencyFormat.format(totalRevenue)
                    : '...',
                icon: Icons.payments_rounded,
                subtitle: 'از ابتدا تاکنون',
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.tertiary.withOpacity(0.7),
                    theme.colorScheme.tertiary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernKpiCard(
                title: 'سفارشات تاخیری',
                value: '...',
                icon: Icons.warning_rounded,
                subtitle: 'نیاز به پیگیری',
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.error.withOpacity(0.7),
                    theme.colorScheme.error,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 32),
        Text(
          'اقدامات سریع',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Column(
              children: [
                _buildQuickActionItem(
                  icon: Icons.person_add_rounded,
                  title: 'افزودن مشتری جدید',
                  subtitle: 'ثبت اطلاعات مشتری جدید',
                  onTap: () {
                    setState(() => _selectedIndex = 2);
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const AddCustomerScreen()));
                  },
                ),
                Divider(height: 1, thickness: 1, indent: 16, endIndent: 16, color: theme.dividerColor.withOpacity(0.6)),
                _buildQuickActionItem(
                  icon: Icons.checklist_rounded,
                  title: 'بررسی سفارشات امروز',
                  subtitle: 'مشاهده و مدیریت سفارشات روز',
                  onTap: () {
                    setState(() => _selectedIndex = 1);
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernKpiCard({
    required String title,
    required String value,
    required IconData icon,
    required String subtitle,
    required Gradient gradient,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.last.withOpacity(0.35),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Icon(
                  icon,
                  color: Colors.white.withOpacity(0.8),
                  size: 24,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildQuickActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 22,
        ),
      ),
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant),
      ),
      trailing: Icon(Icons.arrow_forward_ios_rounded, size: 16, color: theme.colorScheme.onSurface.withOpacity(0.4)),
      onTap: onTap,
    );
  }

  Widget _buildSettingsTab() {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);
    
    return ListView(
      padding: const EdgeInsets.only(top: 8, left: 16, right: 16, bottom: 20),
      children: [
        Center(
          child: Text(
            'تنظیمات',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 24),
        Card(
          elevation: 2,
          margin: EdgeInsets.zero,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              _buildSettingsItem(
                icon: Icons.business_rounded,
                title: 'پروفایل کسب و کار',
                subtitle: 'ویرایش اطلاعات کسب و کار',
                onTap: () {
                  authState.whenData((data) {
                    final (_, profile) = data;
                    if (profile != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditBusinessProfileScreen(
                            profile: profile,
                          ),
                        ),
                      );
                    }
                  });
                },
              ),
              Divider(height: 1, thickness: 1, indent: 70, endIndent: 16, color: theme.dividerColor.withOpacity(0.6)),
              _buildSettingsItem(
                icon: Icons.lock_rounded,
                title: 'تغییر رمز عبور',
                subtitle: 'تغییر رمز عبور حساب کاربری',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ChangePasswordScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          margin: EdgeInsets.zero,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              _buildSettingsItem(
                icon: Icons.help_rounded,
                title: 'راهنما',
                subtitle: 'مشاهده راهنمای استفاده از برنامه',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const HelpScreen(),
                    ),
                  );
                },
              ),
              Divider(height: 1, thickness: 1, indent: 70, endIndent: 16, color: theme.dividerColor.withOpacity(0.6)),
              _buildSettingsItem(
                icon: Icons.info_rounded,
                title: 'درباره برنامه',
                subtitle: 'اطلاعات نسخه و تیم توسعه',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AboutScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          margin: EdgeInsets.zero,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          color: theme.colorScheme.errorContainer,
          child: _buildSettingsItem(
            icon: Icons.logout_rounded,
            iconColor: theme.colorScheme.error,
            title: 'خروج از حساب کاربری',
            subtitle: 'خروج از برنامه و بازگشت به صفحه ورود',
            onTap: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('خروج از حساب کاربری'),
                  content: const Text('آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('انصراف'),
                    ),
                    FilledButton(
                      onPressed: () {
                        Navigator.pop(context);
                        ref.read(authProvider.notifier).signOut();
                      },
                      style: FilledButton.styleFrom(
                        backgroundColor: theme.colorScheme.error,
                      ),
                      child: const Text('خروج'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildSettingsItem({
    required IconData icon,
    Color? iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final color = iconColor ?? theme.colorScheme.primary;
    final bgColor = (iconColor ?? theme.colorScheme.primary).withOpacity(0.1);

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: color,
          size: 22,
        ),
      ),
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant),
      ),
      trailing: Icon(Icons.arrow_forward_ios_rounded, size: 16, color: theme.colorScheme.onSurface.withOpacity(0.4)),
      onTap: onTap,
    );
  }

  Widget _buildAnimatedFAB() {
    final theme = Theme.of(context);
    
    return Container(
      height: 60,
      width: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            Color.lerp(theme.colorScheme.primary, theme.colorScheme.secondary, 0.6)!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.25),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(30),
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: () {
            // Navigate based on current tab
            if (_selectedIndex == 0 || _selectedIndex == 1) {
              // Navigate to add order screen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddOrderScreen(),
                ),
              );
            } else if (_selectedIndex == 2) {
              // Navigate to add customer screen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddCustomerScreen(),
                ),
              );
            }
          },
          child: const Icon(
            Icons.add_rounded,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }
} 