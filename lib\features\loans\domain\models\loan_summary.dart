import 'package:freezed_annotation/freezed_annotation.dart';

part 'loan_summary.freezed.dart';
part 'loan_summary.g.dart';

@freezed
class LoanSummary with _$LoanSummary {
  const factory LoanSummary({
    @Default(0) int totalLoans,
    @Default(0) int activeLoans,
    @Default(0) int overdueLoans,
    @Default(0) int completedLoans,
    @Default(0.0) double totalLoanAmount,
    @Default(0.0) double totalPaidAmount,
    @Default(0.0) double totalOutstandingAmount,
    @Default(0.0) double totalOverdueAmount,
  }) = _LoanSummary;

  factory LoanSummary.fromJson(Map<String, dynamic> json) => _$LoanSummary<PERSON>rom<PERSON>son(json);
}
