// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'

    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBs77GVukBGOCrruJl2BASV-RDZH0mNkDo',
    appId: '1:243095070326:web:746b8b5f5a462ebb752157',
    messagingSenderId: '243095070326',
    projectId: 'tailorbook-df0e8',
    authDomain: 'tailorbook-df0e8.firebaseapp.com',
    storageBucket: 'tailorbook-df0e8.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBw9TPwBH61vbxhJ7ppUPavIJyLdZtrIYQ',
    appId: '1:243095070326:android:990944420608bca3752157',
    messagingSenderId: '243095070326',
    projectId: 'tailorbook-df0e8',
    storageBucket: 'tailorbook-df0e8.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDHTz8yE_p5JjHGyGAfKV4-L4UPVrqFuR4',
    appId: '1:243095070326:ios:5fc7473ef301f142752157',
    messagingSenderId: '243095070326',
    projectId: 'tailorbook-df0e8',
    storageBucket: 'tailorbook-df0e8.firebasestorage.app',
    iosBundleId: 'com.example.tailorFlutter',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDHTz8yE_p5JjHGyGAfKV4-L4UPVrqFuR4',
    appId: '1:243095070326:ios:5fc7473ef301f142752157',
    messagingSenderId: '243095070326',
    projectId: 'tailorbook-df0e8',
    storageBucket: 'tailorbook-df0e8.firebasestorage.app',
    iosBundleId: 'com.example.tailorFlutter',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBs77GVukBGOCrruJl2BASV-RDZH0mNkDo',
    appId: '1:243095070326:web:e2d12f290b5acd0a752157',
    messagingSenderId: '243095070326',
    projectId: 'tailorbook-df0e8',
    authDomain: 'tailorbook-df0e8.firebaseapp.com',
    storageBucket: 'tailorbook-df0e8.firebasestorage.app',
  );
}
