import 'package:firebase_auth/firebase_auth.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/firebase_service.dart';
import '../domain/models/business_profile.dart';

class AuthRepository {
  final _auth = FirebaseService.auth;
  final _firestore = FirebaseService.firestore;

  Stream<User?> authStateChanges() => _auth.authStateChanges();

  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return await _auth.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('کاربر وارد نشده است');

    // Re-authenticate user before changing password
    final credential = EmailAuthProvider.credential(
      email: user.email!,
      password: currentPassword,
    );

    try {
      await user.reauthenticateWithCredential(credential);
      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'wrong-password':
          throw Exception('رمز عبور فعلی اشتباه است');
        case 'weak-password':
          throw Exception('رمز عبور جدید ضعیف است');
        default:
          throw Exception('خطا در تغییر رمز عبور: ${e.message}');
      }
    }
  }

  Future<(UserCredential, BusinessProfile)> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phoneNumber,
    String? address,
  }) async {
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );

    final businessProfile = BusinessProfile(
      id: userCredential.user!.uid,
      businessName: businessName,
      ownerName: ownerName,
      email: email,
      phoneNumber: phoneNumber,
      address: address,
    );

    await _firestore
        .collection('business_profiles')
        .doc(userCredential.user!.uid)
        .set(businessProfile.toJson());

    return (userCredential, businessProfile);
  }

  Future<BusinessProfile?> getBusinessProfile(String userId) async {
    final doc = await _firestore
        .collection('business_profiles')
        .doc(userId)
        .get();

    if (!doc.exists) return null;
    return BusinessProfile.fromJson(doc.data()!);
  }

  Future<void> updateBusinessProfile(BusinessProfile profile) async {
    await _firestore
        .collection('business_profiles')
        .doc(profile.id)
        .update(profile.toJson());
  }

  Future<BusinessProfile> createBusinessProfile({
    required String userId,
    required String email,
    required String businessName,
    required String ownerName,
    String? phoneNumber,
    String? address,
  }) async {
    final businessProfile = BusinessProfile(
      id: userId,
      businessName: businessName,
      ownerName: ownerName,
      email: email,
      phoneNumber: phoneNumber,
      address: address,
    );

    await _firestore
        .collection('business_profiles')
        .doc(userId)
        .set(businessProfile.toJson());

    return businessProfile;
  }
} 