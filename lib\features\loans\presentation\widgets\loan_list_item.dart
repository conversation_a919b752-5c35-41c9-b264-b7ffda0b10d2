import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../domain/models/loan.dart';

class LoanListItem extends StatelessWidget {
  final Loan loan;
  final VoidCallback? onTap;

  const LoanListItem({
    super.key,
    required this.loan,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_IR',
      symbol: 'تومان',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('yyyy/MM/dd', 'fa_IR');

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Loan number and customer
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'وام #${loan.loanNumber}',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (loan.customer != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            loan.customer!.name,
                            style: textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Status badge
                  _buildStatusBadge(theme),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Amount information
              Row(
                children: [
                  Expanded(
                    child: _buildAmountInfo(
                      'مبلغ کل',
                      loan.totalAmount,
                      currencyFormat,
                      theme.colorScheme.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildAmountInfo(
                      'پرداخت شده',
                      loan.paidAmount,
                      currencyFormat,
                      theme.colorScheme.tertiary,
                    ),
                  ),
                  Expanded(
                    child: _buildAmountInfo(
                      'باقی‌مانده',
                      loan.outstandingBalance,
                      currencyFormat,
                      loan.isOverdue 
                        ? theme.colorScheme.error 
                        : theme.colorScheme.secondary,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'پیشرفت پرداخت',
                        style: textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '${loan.paymentPercentage.toStringAsFixed(1)}%',
                        style: textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: loan.paymentPercentage / 100,
                    backgroundColor: theme.colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      loan.isOverdue 
                        ? theme.colorScheme.error
                        : loan.isFullyPaid
                          ? theme.colorScheme.tertiary
                          : theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Footer row
              Row(
                children: [
                  // Creation date
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تاریخ ایجاد: ${dateFormat.format(loan.createdDate)}',
                    style: textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  // Due date or overdue info
                  if (loan.dueDate != null) ...[
                    Icon(
                      loan.isOverdue ? Icons.warning : Icons.schedule,
                      size: 16,
                      color: loan.isOverdue 
                        ? theme.colorScheme.error 
                        : theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      loan.isOverdue 
                        ? '${loan.daysOverdue} روز معوقه'
                        : 'سررسید: ${dateFormat.format(loan.dueDate!)}',
                      style: textTheme.bodySmall?.copyWith(
                        color: loan.isOverdue 
                          ? theme.colorScheme.error 
                          : theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(ThemeData theme) {
    Color backgroundColor;
    Color textColor;
    String text;

    if (loan.isFullyPaid) {
      backgroundColor = theme.colorScheme.tertiaryContainer;
      textColor = theme.colorScheme.onTertiaryContainer;
      text = 'تکمیل شده';
    } else if (loan.isOverdue) {
      backgroundColor = theme.colorScheme.errorContainer;
      textColor = theme.colorScheme.onErrorContainer;
      text = 'معوقه';
    } else {
      backgroundColor = theme.colorScheme.primaryContainer;
      textColor = theme.colorScheme.onPrimaryContainer;
      text = 'فعال';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAmountInfo(
    String label,
    double amount,
    NumberFormat formatter,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          formatter.format(amount),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
