// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaymentImpl _$$PaymentImplFromJson(Map<String, dynamic> json) =>
    _$PaymentImpl(
      id: json['id'] as String,
      loanId: json['loanId'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      method: $enumDecodeNullable(_$PaymentMethodEnumMap, json['method']) ??
          PaymentMethod.cash,
      notes: json['notes'] as String?,
      receiptNumber: json['receiptNumber'] as String?,
    );

Map<String, dynamic> _$$PaymentImplToJson(_$PaymentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'loanId': instance.loanId,
      'amount': instance.amount,
      'paymentDate': instance.paymentDate.toIso8601String(),
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'notes': instance.notes,
      'receiptNumber': instance.receiptNumber,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.cash: 'cash',
  PaymentMethod.card: 'card',
  PaymentMethod.bankTransfer: 'bankTransfer',
  PaymentMethod.check: 'check',
  PaymentMethod.other: 'other',
};
