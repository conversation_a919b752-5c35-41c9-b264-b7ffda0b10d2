import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/firebase_service.dart';
import '../../providers/auth_provider.dart';
import '../widgets/auth_text_field.dart';

class CreateBusinessProfileScreen extends ConsumerStatefulWidget {
  const CreateBusinessProfileScreen({super.key});

  @override
  ConsumerState<CreateBusinessProfileScreen> createState() => _CreateBusinessProfileScreenState();
}

class _CreateBusinessProfileScreenState extends ConsumerState<CreateBusinessProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  bool _isLoading = false;

  Future<void> _createProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final user = FirebaseService.auth.currentUser;
      if (user == null) {
        throw Exception('کاربر وارد نشده است');
      }

      await ref.read(authRepositoryProvider).createBusinessProfile(
        userId: user.uid,
        email: user.email!,
        businessName: _businessNameController.text.trim(),
        ownerName: _ownerNameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        address: _addressController.text.trim(),
      );

      ref.invalidate(authProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('پروفایل کسب و کار با موفقیت ایجاد شد')),
        );
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _ownerNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('ایجاد پروفایل کسب و کار'),
          centerTitle: true,
          elevation: 0,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.business_rounded,
                        size: 48,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'اطلاعات کسب و کار خود را وارد کنید',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  AuthTextField(
                    controller: _businessNameController,
                    labelText: 'نام کسب و کار',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'لطفا نام کسب و کار را وارد کنید';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AuthTextField(
                    controller: _ownerNameController,
                    labelText: 'نام مالک',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'لطفا نام مالک را وارد کنید';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AuthTextField(
                    controller: _phoneController,
                    labelText: 'شماره تماس',
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 16),
                  AuthTextField(
                    controller: _addressController,
                    labelText: 'آدرس',
                    keyboardType: TextInputType.multiline,
                    maxLines: 3,
                  ),
                  const SizedBox(height: 32),
                  FilledButton(
                    onPressed: _isLoading ? null : _createProfile,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('ایجاد پروفایل'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 