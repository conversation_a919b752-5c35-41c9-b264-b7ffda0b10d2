import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/customer.dart';
import '../../../orders/presentation/screens/add_order_screen.dart';
import '../../../orders/presentation/screens/order_detail_screen.dart';
import '../../../orders/presentation/widgets/order_list_item.dart';
import '../../../orders/providers/orders_provider.dart';
import '../../../orders/domain/models/order.dart';
import './edit_customer_screen.dart';
import '../../providers/customers_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class CustomerDetailScreen extends ConsumerWidget {
  final Customer customer;

  const CustomerDetailScreen({
    Key? key,
    required this.customer,
  }) : super(key: key);

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final ordersAsyncValue = ref.watch(ordersProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(customer.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EditCustomerScreen(customer: customer),
                ),
              );
              
              // If customer was updated, pop back to customers screen
              if (result == true) {
                Navigator.pop(context, true);
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteConfirmation(context, ref),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer info card
            Card(
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.person,
                            color: theme.colorScheme.primary,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                customer.name,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.phone,
                                    size: 16,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    customer.phoneNumber,
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    icon: Icon(
                                      Icons.call_rounded,
                                      size: 20,
                                      color: theme.colorScheme.primary,
                                    ),
                                    style: IconButton.styleFrom(
                                      padding: const EdgeInsets.all(8),
                                      backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    onPressed: () => _makePhoneCall(customer.phoneNumber),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),
                    if (customer.address != null && customer.address!.isNotEmpty) ...[
                      _buildInfoRow(
                        context,
                        Icons.location_on_outlined,
                        'آدرس',
                        customer.address!,
                      ),
                      const SizedBox(height: 12),
                    ],
                    // Comment out email section until we resolve the freezed issue
                    // if (customer.email != null && customer.email!.isNotEmpty) ...[
                    //   _buildInfoRow(
                    //     context,
                    //     Icons.email_outlined,
                    //     'ایمیل',
                    //     customer.email!,
                    //   ),
                    //   const SizedBox(height: 12),
                    // ],
                    if (customer.notes != null && customer.notes!.isNotEmpty) ...[
                      _buildInfoRow(
                        context,
                        Icons.note_outlined,
                        'یادداشت‌ها',
                        customer.notes!,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Orders section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'سفارشات',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Order stats cards
            ordersAsyncValue.when(
              data: (orders) {
                final customerOrders = orders
                    .where((order) => order.customerId == customer.id)
                    .toList();
                
                final activeOrders = customerOrders
                    .where((order) => order.status != OrderStatus.completed)
                    .length;

                return Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'کل سفارشات',
                        '${customerOrders.length}',
                        Icons.shopping_bag_outlined,
                        theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'سفارشات فعال',
                        '$activeOrders',
                        Icons.pending_actions_outlined,
                        theme.colorScheme.secondary,
                      ),
                    ),
                  ],
                );
              },
              loading: () => Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'کل سفارشات',
                      '...',
                      Icons.shopping_bag_outlined,
                      theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'سفارشات فعال',
                      '...',
                      Icons.pending_actions_outlined,
                      theme.colorScheme.secondary,
                    ),
                  ),
                ],
              ),
              error: (_, __) => Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'کل سفارشات',
                      '-',
                      Icons.shopping_bag_outlined,
                      theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      context,
                      'سفارشات فعال',
                      '-',
                      Icons.pending_actions_outlined,
                      theme.colorScheme.secondary,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Orders list
            ordersAsyncValue.when(
              data: (orders) {
                final customerOrders = orders
                    .where((order) => order.customerId == customer.id)
                    .toList();

                if (customerOrders.isEmpty) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          Icon(
                            Icons.receipt_long_outlined,
                            size: 64,
                            color: theme.colorScheme.outline,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'هنوز سفارشی ثبت نشده است',
                            style: theme.textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'برای ثبت سفارش جدید روی دکمه + کلیک کنید',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Column(
                  children: customerOrders.map((order) {
                    return OrderListItem(
                      order: order,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OrderDetailScreen(order: order),
                          ),
                        ).then((result) {
                          if (result == true) {
                            ref.refresh(ordersProvider);
                          }
                        });
                      },
                    );
                  }).toList(),
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text('خطا در بارگذاری سفارشات: $error'),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddOrderScreen(),
            ),
          );
        },
        label: const Text('سفارش جدید'),
        icon: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildInfoRow(BuildContext context, IconData icon, String label, String value) {
    final theme = Theme.of(context);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: color,
              size: 28,
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> _showDeleteConfirmation(BuildContext context, WidgetRef ref) async {
    // Check if customer has any orders
    final ordersAsyncValue = ref.watch(ordersProvider);
    
    return ordersAsyncValue.when(
      data: (orders) async {
        final customerOrders = orders
            .where((order) => order.customerId == customer.id)
            .toList();
            
        if (customerOrders.isNotEmpty) {
          // Show error dialog if customer has orders
          await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('حذف مشتری'),
              content: const Text('این مشتری دارای سفارش است و نمی‌توان آن را حذف کرد. ابتدا باید تمام سفارشات مشتری را حذف کنید.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('متوجه شدم'),
                ),
              ],
            ),
          );
          return;
        }
        
        // If no orders, show delete confirmation
        final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('حذف مشتری'),
            content: Text('آیا از حذف "${customer.name}" اطمینان دارید؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('انصراف'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
        );
        
        if (confirmed == true) {
          try {
            await ref.read(customersNotifierProvider.notifier).deleteCustomer(customer.id);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('مشتری با موفقیت حذف شد'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
              Navigator.pop(context, true);
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('خطا در حذف مشتری: ${e.toString()}'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              );
            }
          }
        }
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('در حال بارگذاری اطلاعات...'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
      error: (error, _) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در بارگذاری اطلاعات: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
    );
  }
} 