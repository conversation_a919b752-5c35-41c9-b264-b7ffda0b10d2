import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../domain/models/loan.dart';
import '../../domain/models/payment.dart';
import '../../providers/loans_provider.dart';
import '../widgets/payment_list_item.dart';
import '../widgets/add_payment_dialog.dart';

class LoanDetailScreen extends ConsumerStatefulWidget {
  final Loan loan;

  const LoanDetailScreen({
    super.key,
    required this.loan,
  });

  @override
  ConsumerState<LoanDetailScreen> createState() => _LoanDetailScreenState();
}

class _LoanDetailScreenState extends ConsumerState<LoanDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_IR',
      symbol: 'تومان',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('yyyy/MM/dd', 'fa_IR');
    final paymentsAsync = ref.watch(loanPaymentsProvider(widget.loan.id));

    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text('وام #${widget.loan.loanNumber}'),
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.onSurface,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                // TODO: Navigate to edit loan screen
              },
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'delete') {
                  _showDeleteConfirmation();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف وام'),
                    ],
                  ),
                ),
              ],
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'جزئیات'),
              Tab(text: 'پرداخت‌ها'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildDetailsTab(theme, currencyFormat, dateFormat),
            _buildPaymentsTab(paymentsAsync, theme, currencyFormat),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => _showAddPaymentDialog(),
          icon: const Icon(Icons.payment),
          label: const Text('ثبت پرداخت'),
        ),
      ),
    );
  }

  Widget _buildDetailsTab(
      ThemeData theme, NumberFormat currencyFormat, DateFormat dateFormat) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Loan summary card
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'خلاصه وام',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Amount information
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'مبلغ کل',
                        currencyFormat.format(widget.loan.totalAmount),
                        theme.colorScheme.primary,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'پرداخت شده',
                        currencyFormat.format(widget.loan.paidAmount),
                        theme.colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'باقی‌مانده',
                        currencyFormat.format(widget.loan.outstandingBalance),
                        widget.loan.isOverdue
                            ? theme.colorScheme.error
                            : theme.colorScheme.secondary,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        'درصد پرداخت',
                        '${widget.loan.paymentPercentage.toStringAsFixed(1)}%',
                        theme.colorScheme.outline,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Progress bar
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'پیشرفت پرداخت',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: widget.loan.paymentPercentage / 100,
                      backgroundColor: theme.colorScheme.surfaceVariant,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        widget.loan.isOverdue
                            ? theme.colorScheme.error
                            : widget.loan.isFullyPaid
                                ? theme.colorScheme.tertiary
                                : theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Customer information
        if (widget.loan.customer != null) ...[
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'اطلاعات مشتری',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('نام', widget.loan.customer!.name),
                  _buildInfoRow(
                      'شماره تماس', widget.loan.customer!.phoneNumber),
                  if (widget.loan.customer!.address != null)
                    _buildInfoRow('آدرس', widget.loan.customer!.address!),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Loan details
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'جزئیات وام',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildInfoRow('شماره وام', widget.loan.loanNumber),
                _buildInfoRow(
                    'تاریخ ایجاد', dateFormat.format(widget.loan.createdDate)),
                if (widget.loan.dueDate != null)
                  _buildInfoRow(
                    'تاریخ سررسید',
                    dateFormat.format(widget.loan.dueDate!),
                    isOverdue: widget.loan.isOverdue,
                  ),
                if (widget.loan.description != null)
                  _buildInfoRow('توضیحات', widget.loan.description!),
                if (widget.loan.notes != null)
                  _buildInfoRow('یادداشت‌ها', widget.loan.notes!),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentsTab(AsyncValue<List<Payment>> paymentsAsync,
      ThemeData theme, NumberFormat currencyFormat) {
    return paymentsAsync.when(
      data: (payments) {
        if (payments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment_outlined,
                  size: 64,
                  color: theme.colorScheme.outline,
                ),
                const SizedBox(height: 16),
                Text(
                  'هنوز پرداختی ثبت نشده است',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'برای ثبت پرداخت جدید روی دکمه زیر کلیک کنید',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: payments.length,
          itemBuilder: (context, index) {
            return PaymentListItem(
              payment: payments[index],
              onDelete: () => _deletePayment(payments[index]),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64),
            const SizedBox(height: 16),
            Text('خطا در بارگذاری پرداخت‌ها: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () =>
                  ref.refresh(loanPaymentsProvider(widget.loan.id)),
              child: const Text('تلاش مجدد'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isOverdue = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isOverdue ? Colors.red : null,
                fontWeight: isOverdue ? FontWeight.bold : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddPaymentDialog() {
    showDialog(
      context: context,
      builder: (context) => AddPaymentDialog(
        loanId: widget.loan.id,
        outstandingBalance: widget.loan.outstandingBalance,
      ),
    );
  }

  void _deletePayment(Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف پرداخت'),
        content:
            const Text('آیا مطمئن هستید که می‌خواهید این پرداخت را حذف کنید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('انصراف'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await ref.read(loansNotifierProvider.notifier).deletePayment(
                      loanId: widget.loan.id,
                      paymentId: payment.id,
                      paymentAmount: payment.amount,
                    );
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('پرداخت با موفقیت حذف شد'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطا در حذف پرداخت: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف وام'),
        content: const Text(
            'آیا مطمئن هستید که می‌خواهید این وام را حذف کنید؟ این عمل قابل بازگشت نیست.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('انصراف'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await ref
                    .read(loansNotifierProvider.notifier)
                    .deleteLoan(widget.loan.id);
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('وام با موفقیت حذف شد'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطا در حذف وام: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
