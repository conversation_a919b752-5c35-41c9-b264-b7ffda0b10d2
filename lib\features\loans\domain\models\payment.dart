import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment.freezed.dart';
part 'payment.g.dart';

enum PaymentMethod {
  cash,
  card,
  bankTransfer,
  check,
  other,
}

@freezed
class Payment with _$Payment {
  const factory Payment({
    required String id,
    required String loanId,
    required double amount,
    required DateTime paymentDate,
    @Default(PaymentMethod.cash) PaymentMethod method,
    String? notes,
    String? receiptNumber,
  }) = _Payment;

  factory Payment.fromJson(Map<String, dynamic> json) => _$PaymentFromJson(json);
}
