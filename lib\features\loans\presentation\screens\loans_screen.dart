import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/loans_provider.dart';
import '../../domain/models/loan.dart';
import '../widgets/loan_list_item.dart';
import '../widgets/loan_summary_card.dart';
import 'add_loan_screen.dart';
import 'loan_detail_screen.dart';

class LoansScreen extends ConsumerStatefulWidget {
  const LoansScreen({super.key});

  @override
  ConsumerState<LoansScreen> createState() => _LoansScreenState();
}

class _LoansScreenState extends ConsumerState<LoansScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final loansAsync = ref.watch(loansProvider);
    final loanSummaryAsync = ref.watch(loanSummaryProvider);

    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('مدیریت وام‌ها'),
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.onSurface,
          elevation: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(100),
            child: Column(
              children: [
                // Search bar
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'جستجو در وام‌ها...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor:
                          theme.colorScheme.surfaceVariant.withOpacity(0.5),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.toLowerCase();
                      });
                    },
                  ),
                ),
                // Tab bar
                TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'همه'),
                    Tab(text: 'فعال'),
                    Tab(text: 'معوقه'),
                    Tab(text: 'تکمیل شده'),
                  ],
                ),
              ],
            ),
          ),
        ),
        body: Column(
          children: [
            // Summary cards
            loanSummaryAsync.when(
              data: (summary) => LoanSummaryCard(summary: summary),
              loading: () => const SizedBox(
                height: 120,
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => const SizedBox.shrink(),
            ),
            // Loans list
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLoansList(loansAsync, null),
                  _buildLoansList(loansAsync, LoanStatus.active),
                  _buildLoansList(loansAsync, 'overdue'),
                  _buildLoansList(loansAsync, LoanStatus.completed),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AddLoanScreen()),
            );
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildLoansList(AsyncValue<List<Loan>> loansAsync, dynamic filter) {
    return loansAsync.when(
      data: (loans) {
        List<Loan> filteredLoans = loans;

        // Apply status filter
        if (filter == LoanStatus.active) {
          filteredLoans = loans
              .where((loan) =>
                  loan.status == LoanStatus.active && !loan.isFullyPaid)
              .toList();
        } else if (filter == 'overdue') {
          filteredLoans = loans.where((loan) => loan.isOverdue).toList();
        } else if (filter == LoanStatus.completed) {
          filteredLoans = loans
              .where((loan) =>
                  loan.isFullyPaid || loan.status == LoanStatus.completed)
              .toList();
        }

        // Apply search filter
        if (_searchQuery.isNotEmpty) {
          filteredLoans = filteredLoans.where((loan) {
            final customerName = loan.customer?.name.toLowerCase() ?? '';
            final loanNumber = loan.loanNumber.toLowerCase();
            final description = loan.description?.toLowerCase() ?? '';

            return customerName.contains(_searchQuery) ||
                loanNumber.contains(_searchQuery) ||
                description.contains(_searchQuery);
          }).toList();
        }

        if (filteredLoans.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.outline,
                ),
                const SizedBox(height: 16),
                Text(
                  _searchQuery.isNotEmpty
                      ? 'هیچ وامی یافت نشد'
                      : 'هنوز وامی ثبت نشده است',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredLoans.length,
          itemBuilder: (context, index) {
            return LoanListItem(
              loan: filteredLoans[index],
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        LoanDetailScreen(loan: filteredLoans[index]),
                  ),
                );
              },
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64),
            const SizedBox(height: 16),
            Text('خطا در بارگذاری وام‌ها: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(loansProvider),
              child: const Text('تلاش مجدد'),
            ),
          ],
        ),
      ),
    );
  }
}
