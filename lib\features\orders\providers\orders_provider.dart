import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../features/auth/providers/auth_provider.dart';
import '../data/orders_repository.dart';
import '../domain/models/order.dart';
import '../domain/models/measurement.dart';
import '../../../features/customers/providers/customers_provider.dart';
import '../../../features/customers/domain/models/customer.dart';
import 'package:async/async.dart';

part 'orders_provider.g.dart';

@riverpod
OrdersRepository ordersRepository(OrdersRepositoryRef ref) {
  return OrdersRepository();
}

@riverpod
Stream<List<Order>> orders(OrdersRef ref) async* {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    yield [];
    return;
  }

  // Create a new stream controller to manage the combined stream
  final ordersStream = ref.watch(ordersRepositoryProvider).watchOrders(profile.id);
  final customersStream = ref.watch(customersProvider.stream);

  // Use StreamZip to combine both streams
  final streamGroup = StreamZip([ordersStream, customersStream]);

  try {
    await for (final values in streamGroup) {
      if (values.length == 2) {
        final List<Order> ordersList = values[0] as List<Order>;
        final List<Customer> customersList = values[1] as List<Customer>;
        
        final customerMap = { for (var customer in customersList) customer.id : customer };
        
        final ordersWithCustomers = ordersList.map((order) {
          final customer = customerMap[order.customerId];
          return order.copyWith(customer: customer);
        }).toList();
        
        yield ordersWithCustomers;
      } else {
        yield [];
      } 
    }
  } catch (e) {
    // Handle any stream errors
    yield [];
    rethrow;
  }
}

@riverpod
class OrdersNotifier extends _$OrdersNotifier {
  @override
  FutureOr<void> build() async {}

  Future<void> addOrder({
    required String customerId,
    required String receiptNumber,
    required double price,
    required DateTime orderDate,
    required DateTime deliveryDate,
    required OrderStatus status,
    String? notes,
    bool isPaid = false,
    Measurement? measurement,
  }) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    final order = Order(
      id: '',
      customerId: customerId,
      receiptNumber: receiptNumber,
      price: price,
      orderDate: orderDate,
      deliveryDate: deliveryDate,
      status: status,
      notes: notes,
      isPaid: isPaid,
      measurement: measurement,
    );

    await ref
        .read(ordersRepositoryProvider)
        .addOrder(profile.id, order);
    
    // Invalidate the orders provider to trigger a refresh
    ref.invalidate(ordersProvider);
  }

  Future<void> updateOrder(Order order) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(ordersRepositoryProvider)
        .updateOrder(profile.id, order);
    
    // Invalidate the orders provider to trigger a refresh
    ref.invalidate(ordersProvider);
  }

  Future<void> deleteOrder(String orderId) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(ordersRepositoryProvider)
        .deleteOrder(profile.id, orderId);
    
    // Invalidate the orders provider to trigger a refresh
    ref.invalidate(ordersProvider);
  }
} 