import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/constants.dart';

class AppCard extends StatelessWidget {
  final Widget child;
  final Color? color;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? shadows;
  final Border? border;
  final VoidCallback? onTap;
  final bool isLoading;

  const AppCard({
    super.key,
    required this.child,
    this.color,
    this.padding,
    this.borderRadius,
    this.shadows,
    this.border,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final content = Container(
      padding: padding ?? AppSpacing.cardPadding,
      decoration: BoxDecoration(
        color: color ?? AppColors.background,
        borderRadius: borderRadius ?? AppRadius.cardRadius,
        boxShadow: shadows ?? AppShadows.sm,
        border: border,
      ),
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? AppRadius.cardRadius,
        child: content,
      );
    }

    return content;
  }
} 