import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../features/auth/providers/auth_provider.dart';
import '../data/loans_repository.dart';
import '../domain/models/loan.dart';
import '../domain/models/payment.dart';
import '../domain/models/loan_summary.dart';

part 'loans_provider.g.dart';

@riverpod
LoansRepository loansRepository(LoansRepositoryRef ref) {
  return LoansRepository();
}

@riverpod
Stream<List<Loan>> loans(LoansRef ref) async* {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    yield [];
    return;
  }

  yield* ref.watch(loansRepositoryProvider).watchLoans(profile.id);
}

@riverpod
Stream<List<Loan>> customerLoans(CustomerLoansRef ref, String customerId) async* {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    yield [];
    return;
  }

  yield* ref.watch(loansRepositoryProvider).watchCustomerLoans(profile.id, customerId);
}

@riverpod
Stream<List<Payment>> loanPayments(LoanPaymentsRef ref, String loanId) async* {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    yield [];
    return;
  }

  yield* ref.watch(loansRepositoryProvider).watchLoanPayments(profile.id, loanId);
}

@riverpod
Future<LoanSummary> loanSummary(LoanSummaryRef ref) async {
  final authState = await ref.watch(authProvider.future);
  final (_, profile) = authState;
  
  if (profile == null) {
    return const LoanSummary();
  }

  return await ref.watch(loansRepositoryProvider).getLoanSummary(profile.id);
}

@riverpod
class LoansNotifier extends _$LoansNotifier {
  @override
  FutureOr<void> build() async {}

  Future<void> addLoan({
    required String customerId,
    required String loanNumber,
    required double totalAmount,
    required DateTime createdDate,
    DateTime? dueDate,
    String? description,
    String? notes,
  }) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    final loan = Loan(
      id: '',
      customerId: customerId,
      loanNumber: loanNumber,
      totalAmount: totalAmount,
      paidAmount: 0.0,
      createdDate: createdDate,
      dueDate: dueDate,
      description: description,
      notes: notes,
    );

    await ref
        .read(loansRepositoryProvider)
        .addLoan(profile.id, loan);
  }

  Future<void> updateLoan(Loan loan) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(loansRepositoryProvider)
        .updateLoan(profile.id, loan);
  }

  Future<void> deleteLoan(String loanId) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(loansRepositoryProvider)
        .deleteLoan(profile.id, loanId);
  }

  Future<void> addPayment({
    required String loanId,
    required double amount,
    required DateTime paymentDate,
    PaymentMethod method = PaymentMethod.cash,
    String? notes,
    String? receiptNumber,
  }) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    final payment = Payment(
      id: '',
      loanId: loanId,
      amount: amount,
      paymentDate: paymentDate,
      method: method,
      notes: notes,
      receiptNumber: receiptNumber,
    );

    await ref
        .read(loansRepositoryProvider)
        .addPayment(profile.id, loanId, payment);
  }

  Future<void> deletePayment({
    required String loanId,
    required String paymentId,
    required double paymentAmount,
  }) async {
    final authState = await ref.read(authProvider.future);
    final (_, profile) = authState;
    if (profile == null) return;

    await ref
        .read(loansRepositoryProvider)
        .deletePayment(profile.id, loanId, paymentId, paymentAmount);
  }
}
