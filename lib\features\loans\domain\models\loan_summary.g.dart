// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_summary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LoanSummaryImpl _$$LoanSummaryImplFromJson(Map<String, dynamic> json) =>
    _$LoanSummaryImpl(
      totalLoans: (json['totalLoans'] as num?)?.toInt() ?? 0,
      activeLoans: (json['activeLoans'] as num?)?.toInt() ?? 0,
      overdueLoans: (json['overdueLoans'] as num?)?.toInt() ?? 0,
      completedLoans: (json['completedLoans'] as num?)?.toInt() ?? 0,
      totalLoanAmount: (json['totalLoanAmount'] as num?)?.toDouble() ?? 0.0,
      totalPaidAmount: (json['totalPaidAmount'] as num?)?.toDouble() ?? 0.0,
      totalOutstandingAmount:
          (json['totalOutstandingAmount'] as num?)?.toDouble() ?? 0.0,
      totalOverdueAmount:
          (json['totalOverdueAmount'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$LoanSummaryImplToJson(_$LoanSummaryImpl instance) =>
    <String, dynamic>{
      'totalLoans': instance.totalLoans,
      'activeLoans': instance.activeLoans,
      'overdueLoans': instance.overdueLoans,
      'completedLoans': instance.completedLoans,
      'totalLoanAmount': instance.totalLoanAmount,
      'totalPaidAmount': instance.totalPaidAmount,
      'totalOutstandingAmount': instance.totalOutstandingAmount,
      'totalOverdueAmount': instance.totalOverdueAmount,
    };
