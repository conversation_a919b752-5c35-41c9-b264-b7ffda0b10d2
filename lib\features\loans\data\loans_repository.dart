import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/firebase_service.dart';
import '../domain/models/loan.dart';
import '../domain/models/payment.dart';
import '../domain/models/loan_summary.dart';

class LoansRepository {
  final _firestore = FirebaseService.firestore;

  // Watch all loans for a business
  Stream<List<Loan>> watchLoans(String businessId) {
    return _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .orderBy('createdDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Loan.fromJson({...doc.data(), 'id': doc.id}))
            .toList());
  }

  // Watch loans for a specific customer
  Stream<List<Loan>> watchCustomerLoans(String businessId, String customerId) {
    return _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .where('customerId', isEqualTo: customerId)
        .orderBy('createdDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Loan.fromJson({...doc.data(), 'id': doc.id}))
            .toList());
  }

  // Add a new loan
  Future<void> addLoan(String businessId, Loan loan) async {
    final loanData = loan.toJson()..remove('id');
    // Remove customer and payments from the main document
    loanData.remove('customer');
    loanData.remove('payments');

    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .add(loanData);
  }

  // Update an existing loan
  Future<void> updateLoan(String businessId, Loan loan) async {
    final loanData = loan.toJson()..remove('id');
    // Remove customer and payments from the main document
    loanData.remove('customer');
    loanData.remove('payments');

    await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loan.id)
        .update(loanData);
  }

  // Delete a loan
  Future<void> deleteLoan(String businessId, String loanId) async {
    final batch = _firestore.batch();

    // Delete the loan document
    final loanRef = _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId);
    batch.delete(loanRef);

    // Delete all payments for this loan
    final paymentsSnapshot = await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId)
        .collection('payments')
        .get();

    for (final doc in paymentsSnapshot.docs) {
      batch.delete(doc.reference);
    }

    await batch.commit();
  }

  // Get a single loan
  Future<Loan?> getLoan(String businessId, String loanId) async {
    final doc = await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId)
        .get();

    if (!doc.exists) return null;
    return Loan.fromJson({...doc.data()!, 'id': doc.id});
  }

  // Watch payments for a specific loan
  Stream<List<Payment>> watchLoanPayments(String businessId, String loanId) {
    return _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId)
        .collection('payments')
        .orderBy('paymentDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Payment.fromJson({...doc.data(), 'id': doc.id}))
            .toList());
  }

  // Add a payment to a loan
  Future<void> addPayment(String businessId, String loanId, Payment payment) async {
    final batch = _firestore.batch();

    // Add the payment
    final paymentData = payment.toJson()..remove('id');
    final paymentRef = _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId)
        .collection('payments')
        .doc();
    batch.set(paymentRef, paymentData);

    // Update the loan's paid amount
    final loanRef = _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId);
    
    batch.update(loanRef, {
      'paidAmount': FieldValue.increment(payment.amount),
    });

    await batch.commit();
  }

  // Delete a payment
  Future<void> deletePayment(String businessId, String loanId, String paymentId, double paymentAmount) async {
    final batch = _firestore.batch();

    // Delete the payment
    final paymentRef = _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId)
        .collection('payments')
        .doc(paymentId);
    batch.delete(paymentRef);

    // Update the loan's paid amount
    final loanRef = _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .doc(loanId);
    
    batch.update(loanRef, {
      'paidAmount': FieldValue.increment(-paymentAmount),
    });

    await batch.commit();
  }

  // Get loan summary statistics
  Future<LoanSummary> getLoanSummary(String businessId) async {
    final snapshot = await _firestore
        .collection('business_profiles')
        .doc(businessId)
        .collection('loans')
        .get();

    if (snapshot.docs.isEmpty) {
      return const LoanSummary();
    }

    final loans = snapshot.docs
        .map((doc) => Loan.fromJson({...doc.data(), 'id': doc.id}))
        .toList();

    int totalLoans = loans.length;
    int activeLoans = 0;
    int overdueLoans = 0;
    int completedLoans = 0;
    double totalLoanAmount = 0.0;
    double totalPaidAmount = 0.0;
    double totalOutstandingAmount = 0.0;
    double totalOverdueAmount = 0.0;

    for (final loan in loans) {
      totalLoanAmount += loan.totalAmount;
      totalPaidAmount += loan.paidAmount;
      totalOutstandingAmount += loan.outstandingBalance;

      if (loan.isFullyPaid) {
        completedLoans++;
      } else {
        activeLoans++;
        if (loan.isOverdue) {
          overdueLoans++;
          totalOverdueAmount += loan.outstandingBalance;
        }
      }
    }

    return LoanSummary(
      totalLoans: totalLoans,
      activeLoans: activeLoans,
      overdueLoans: overdueLoans,
      completedLoans: completedLoans,
      totalLoanAmount: totalLoanAmount,
      totalPaidAmount: totalPaidAmount,
      totalOutstandingAmount: totalOutstandingAmount,
      totalOverdueAmount: totalOverdueAmount,
    );
  }
}
