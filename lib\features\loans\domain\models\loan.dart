import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../customers/domain/models/customer.dart';
import 'payment.dart';

part 'loan.freezed.dart';
part 'loan.g.dart';

enum LoanStatus {
  active,
  completed,
  overdue,
  cancelled,
}

@freezed
class Loan with _$Loan {
  const Loan._();

  const factory Loan({
    required String id,
    required String customerId,
    required String loanNumber,
    required double totalAmount,
    required double paidAmount,
    required DateTime createdDate,
    DateTime? dueDate,
    @Default(LoanStatus.active) LoanStatus status,
    String? description,
    String? notes,
    Customer? customer,
    @Default([]) List<Payment> payments,
  }) = _Loan;

  factory Loan.fromJson(Map<String, dynamic> json) => _$LoanFromJson(json);

  // Calculate outstanding balance
  double get outstandingBalance => totalAmount - paidAmount;

  // Check if loan is fully paid
  bool get isFullyPaid => paidAmount >= totalAmount;

  // Check if loan is overdue
  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!) && !isFullyPaid;
  }

  // Get payment percentage
  double get paymentPercentage {
    if (totalAmount == 0) return 0;
    return (paidAmount / totalAmount) * 100;
  }

  // Get days overdue (returns 0 if not overdue)
  int get daysOverdue {
    if (!isOverdue) return 0;
    return DateTime.now().difference(dueDate!).inDays;
  }
}
