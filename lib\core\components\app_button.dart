import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/constants.dart';
import '../theme/text_styles.dart';

enum AppButtonVariant {
  primary,
  secondary,
  outlined,
  text,
}

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonVariant variant;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;
  final EdgeInsets? padding;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = AppButtonVariant.primary,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.padding,
    this.width,
    this.height,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    // final theme = Theme.of(context);

    Widget button;
    switch (variant) {
      case AppButtonVariant.primary:
        button = FilledButton(
          onPressed: isLoading ? null : onPressed,
          style: FilledButton.styleFrom(
            backgroundColor: backgroundColor ?? AppColors.primary,
            foregroundColor: foregroundColor ?? Colors.white,
            padding: padding ?? AppSpacing.buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: AppRadius.buttonRadius,
            ),
          ),
          child: _buildChild(),
        );
        break;

      case AppButtonVariant.secondary:
        button = FilledButton.tonal(
          onPressed: isLoading ? null : onPressed,
          style: FilledButton.styleFrom(
            backgroundColor: backgroundColor ?? AppColors.primaryContainer,
            foregroundColor: foregroundColor ?? AppColors.primary,
            padding: padding ?? AppSpacing.buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: AppRadius.buttonRadius,
            ),
          ),
          child: _buildChild(),
        );
        break;

      case AppButtonVariant.outlined:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: foregroundColor ?? AppColors.primary,
            padding: padding ?? AppSpacing.buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: AppRadius.buttonRadius,
            ),
            side: BorderSide(
              color: onPressed == null
                  ? AppColors.border
                  : (foregroundColor ?? AppColors.primary),
            ),
          ),
          child: _buildChild(),
        );
        break;

      case AppButtonVariant.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: foregroundColor ?? AppColors.primary,
            padding: padding ?? AppSpacing.buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: AppRadius.buttonRadius,
            ),
          ),
          child: _buildChild(),
        );
        break;
    }

    if (width != null || isFullWidth) {
      return SizedBox(
        width: width ?? double.infinity,
        height: height,
        child: button,
      );
    }

    return button;
  }

  Widget _buildChild() {
    if (isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            variant == AppButtonVariant.primary
                ? Colors.white
                : AppColors.primary,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        textDirection: TextDirection.rtl,
        children: [
          icon!,
          AppSpacing.gapSM,
          Text(
            text,
            style: AppTextStyles.titleMedium.copyWith(
              color: variant == AppButtonVariant.primary
                  ? Colors.white
                  : AppColors.primary,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: AppTextStyles.titleMedium.copyWith(
        color: variant == AppButtonVariant.primary
            ? Colors.white
            : AppColors.primary,
      ),
    );
  }
} 