// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BusinessProfileImpl _$$BusinessProfileImplFromJson(
        Map<String, dynamic> json) =>
    _$BusinessProfileImpl(
      id: json['id'] as String,
      businessName: json['businessName'] as String,
      ownerName: json['ownerName'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      address: json['address'] as String?,
      totalCustomers: (json['totalCustomers'] as num?)?.toInt() ?? 0,
      activeOrders: (json['activeOrders'] as num?)?.toInt() ?? 0,
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$BusinessProfileImplToJson(
        _$BusinessProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessName': instance.businessName,
      'ownerName': instance.ownerName,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'totalCustomers': instance.totalCustomers,
      'activeOrders': instance.activeOrders,
      'totalRevenue': instance.totalRevenue,
    };
