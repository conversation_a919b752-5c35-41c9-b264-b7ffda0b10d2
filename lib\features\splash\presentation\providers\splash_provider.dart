import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/providers/auth_provider.dart';

enum SplashState {
  loading,
  authenticated,
  unauthenticated,
}

final splashProvider = StateNotifierProvider<SplashNotifier, SplashState>((ref) {
  return SplashNotifier(ref);
});

class SplashNotifier extends StateNotifier<SplashState> {
  final Ref ref;

  SplashNotifier(this.ref) : super(SplashState.loading) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Wait for a minimum splash duration
      await Future.delayed(const Duration(seconds: 2));
      
      // Check authentication state
      final authState = await ref.read(authProvider.future);
      final (user, profile) = authState;
      
      if (user != null && profile != null) {
        state = SplashState.authenticated;
      } else {
        state = SplashState.unauthenticated;
      }
    } catch (e) {
      // If there's an error checking auth state, go to login
      state = SplashState.unauthenticated;
    }
  }
} 