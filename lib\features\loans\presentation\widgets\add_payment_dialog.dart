import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:persian_datetime_picker/persian_datetime_picker.dart';
import 'package:intl/intl.dart';
import '../../domain/models/payment.dart';
import '../../providers/loans_provider.dart';

class AddPaymentDialog extends ConsumerStatefulWidget {
  final String loanId;
  final double outstandingBalance;

  const AddPaymentDialog({
    super.key,
    required this.loanId,
    required this.outstandingBalance,
  });

  @override
  ConsumerState<AddPaymentDialog> createState() => _AddPaymentDialogState();
}

class _AddPaymentDialogState extends ConsumerState<AddPaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _receiptController = TextEditingController();
  
  DateTime _paymentDate = DateTime.now();
  PaymentMethod _selectedMethod = PaymentMethod.cash;
  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _receiptController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(
      locale: 'fa_IR',
      symbol: 'تومان',
      decimalDigits: 0,
    );

    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: AlertDialog(
        title: const Text('ثبت پرداخت جدید'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Outstanding balance info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'مبلغ باقی‌مانده: ${currencyFormat.format(widget.outstandingBalance)}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Amount field
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'مبلغ پرداخت (تومان)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'مبلغ پرداخت الزامی است';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'مبلغ پرداخت باید عددی مثبت باشد';
                    }
                    if (amount > widget.outstandingBalance) {
                      return 'مبلغ پرداخت نمی‌تواند بیشتر از مبلغ باقی‌مانده باشد';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Payment date
                InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'تاریخ پرداخت',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      Jalali.fromDateTime(_paymentDate).formatCompactDate(),
                      style: theme.textTheme.bodyLarge,
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Payment method
                DropdownButtonFormField<PaymentMethod>(
                  value: _selectedMethod,
                  decoration: const InputDecoration(
                    labelText: 'روش پرداخت',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.payment),
                  ),
                  items: PaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(_getPaymentMethodName(method)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedMethod = value;
                      });
                    }
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Receipt number
                TextFormField(
                  controller: _receiptController,
                  decoration: const InputDecoration(
                    labelText: 'شماره رسید (اختیاری)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.receipt),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Notes
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'یادداشت (اختیاری)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            child: const Text('انصراف'),
          ),
          FilledButton(
            onPressed: _isLoading ? null : _submitPayment,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('ثبت پرداخت'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showPersianDatePicker(
      context: context,
      initialDate: Jalali.fromDateTime(_paymentDate),
      firstDate: Jalali(1400),
      lastDate: Jalali.now(),
    );

    if (picked != null) {
      setState(() {
        _paymentDate = picked.toDateTime();
      });
    }
  }

  Future<void> _submitPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(loansNotifierProvider.notifier).addPayment(
        loanId: widget.loanId,
        amount: double.parse(_amountController.text.trim()),
        paymentDate: _paymentDate,
        method: _selectedMethod,
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        receiptNumber: _receiptController.text.trim().isEmpty 
            ? null 
            : _receiptController.text.trim(),
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('پرداخت با موفقیت ثبت شد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطا در ثبت پرداخت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدی';
      case PaymentMethod.card:
        return 'کارت';
      case PaymentMethod.bankTransfer:
        return 'انتقال بانکی';
      case PaymentMethod.check:
        return 'چک';
      case PaymentMethod.other:
        return 'سایر';
    }
  }
}
