// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'measurement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MeasurementImpl _$$MeasurementImplFromJson(Map<String, dynamic> json) =>
    _$MeasurementImpl(
      height: (json['height'] as num).toDouble(),
      sleeveLength: (json['sleeveLength'] as num).toDouble(),
      sleeveType: $enumDecode(_$SleeveTypeEnumMap, json['sleeveType']),
      shoulderWidth: (json['shoulderWidth'] as num).toDouble(),
      neckCircumference: (json['neckCircumference'] as num).toDouble(),
      collarType: $enumDecode(_$CollarTypeEnumMap, json['collarType']),
      sideWidth: (json['sideWidth'] as num).toDouble(),
      skirtLength: (json['skirtLength'] as num).toDouble(),
      skirtType: $enumDecode(_$SkirtTypeEnumMap, json['skirtType']),
      trouserLength: (json['trouserLength'] as num).toDouble(),
      trouserType: $enumDecode(_$TrouserTypeEnumMap, json['trouserType']),
      hemWidth: (json['hemWidth'] as num).toDouble(),
      hasTrouserPocket: json['hasTrouserPocket'] as bool? ?? false,
    );

Map<String, dynamic> _$$MeasurementImplToJson(_$MeasurementImpl instance) =>
    <String, dynamic>{
      'height': instance.height,
      'sleeveLength': instance.sleeveLength,
      'sleeveType': _$SleeveTypeEnumMap[instance.sleeveType]!,
      'shoulderWidth': instance.shoulderWidth,
      'neckCircumference': instance.neckCircumference,
      'collarType': _$CollarTypeEnumMap[instance.collarType]!,
      'sideWidth': instance.sideWidth,
      'skirtLength': instance.skirtLength,
      'skirtType': _$SkirtTypeEnumMap[instance.skirtType]!,
      'trouserLength': instance.trouserLength,
      'trouserType': _$TrouserTypeEnumMap[instance.trouserType]!,
      'hemWidth': instance.hemWidth,
      'hasTrouserPocket': instance.hasTrouserPocket,
    };

const _$SleeveTypeEnumMap = {
  SleeveType.plain: 'plain',
  SleeveType.cuffed: 'cuffed',
  SleeveType.cufflinks: 'cufflinks',
  SleeveType.banded: 'banded',
  SleeveType.boat: 'boat',
  SleeveType.stitchedEdge: 'stitchedEdge',
};

const _$CollarTypeEnumMap = {
  CollarType.hindi: 'hindi',
  CollarType.pakistani: 'pakistani',
  CollarType.half: 'half',
  CollarType.reverse: 'reverse',
  CollarType.qasemi: 'qasemi',
};

const _$SkirtTypeEnumMap = {
  SkirtType.punjabi: 'punjabi',
  SkirtType.round: 'round',
};

const _$TrouserTypeEnumMap = {
  TrouserType.regular: 'regular',
  TrouserType.wide: 'wide',
  TrouserType.tight: 'tight',
};
