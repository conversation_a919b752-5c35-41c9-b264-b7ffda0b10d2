import 'package:freezed_annotation/freezed_annotation.dart';

part 'business_profile.freezed.dart';
part 'business_profile.g.dart';

@freezed
class BusinessProfile with _$BusinessProfile {
  const factory BusinessProfile({
    required String id,
    required String businessName,
    required String ownerName,
    required String email,
    String? phoneNumber,
    String? address,
    @Default(0) int totalCustomers,
    @Default(0) int activeOrders,
    @Default(0) double totalRevenue,
  }) = _BusinessProfile;

  factory BusinessProfile.fromJson(Map<String, dynamic> json) =>
      _$BusinessProfileFromJson(json);
} 