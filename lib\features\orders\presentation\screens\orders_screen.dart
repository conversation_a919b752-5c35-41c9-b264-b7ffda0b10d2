import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/orders_provider.dart';
import '../../domain/models/order.dart';
import '../widgets/order_list_item.dart';
import 'add_order_screen.dart';
import 'order_detail_screen.dart';
import 'package:tailor_flutter/core/theme/colors.dart';
import '../../../customers/providers/customers_provider.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> with SingleTickerProviderStateMixin {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  OrderStatus? _selectedStatus;
  bool _isSearchFocused = false;
  final _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final shouldBeScrolled = _scrollController.offset > 10;
    if (shouldBeScrolled != _isScrolled) {
      setState(() => _isScrolled = shouldBeScrolled);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersState = ref.watch(ordersProvider);
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('سفارشات'),
        elevation: 0,
        scrolledUnderElevation: _isScrolled ? 2 : 0,
        shadowColor: theme.shadowColor.withOpacity(0.1),
        surfaceTintColor: Colors.transparent,
        backgroundColor: theme.colorScheme.surface,
      ),
      body: Column(
        children: [
          _buildSearchHeader(theme, textTheme),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                // Invalidate both providers to force a complete refresh
                ref.invalidate(ordersProvider);
                ref.invalidate(customersProvider);
                // Wait for the new data
                await ref.read(ordersProvider.future);
              },
              child: ordersState.when(
                data: (orders) {
                  final filteredOrders = orders.where((order) {
                    if (_selectedStatus != null &&
                        order.status != _selectedStatus) {
                      return false;
                    }

                    if (_searchQuery.isEmpty) return true;

                    final searchLower = _searchQuery.toLowerCase();
                    final receiptMatch = order.receiptNumber?.toLowerCase().contains(searchLower) ?? false;
                    final customerMatch = order.customer?.name.toLowerCase().contains(searchLower) ?? false;
                    
                    return receiptMatch || customerMatch;
                  }).toList();

                  if (filteredOrders.isEmpty) {
                    return _buildEmptyState(theme, textTheme);
                  }

                  return ListView.separated(
                    controller: _scrollController,
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
                    itemCount: filteredOrders.length,
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final order = filteredOrders[index];
                      return OrderListItem(
                        order: order,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => OrderDetailScreen(order: order),
                            ),
                          ).then((result) {
                            if (result == true) {
                              // Invalidate providers to refresh the list
                              ref.invalidate(ordersProvider);
                              ref.invalidate(customersProvider);
                            }
                          });
                        },
                      );
                    },
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => _buildErrorState(theme, textTheme, error),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddOrderScreen(),
            ),
          ).then((_) {
            // Invalidate providers when returning from AddOrderScreen
            ref.invalidate(ordersProvider);
            ref.invalidate(customersProvider);
          });
        },
        tooltip: 'افزودن سفارش جدید',
        child: const Icon(
          Icons.add_rounded,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildSearchHeader(ThemeData theme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: _isScrolled
            ? [
                BoxShadow(
                  color: theme.shadowColor.withOpacity(0.06),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : [],
        border: Border(
           bottom: BorderSide(color: theme.dividerColor.withOpacity(0.5), width: 1)
         )
      ),
      child: Column(
        children: [
          SizedBox(
            height: 48,
            child: TextField(
              controller: _searchController,
              focusNode: FocusNode()..addListener(() {
                  setState(() => _isSearchFocused = FocusScope.of(context).hasFocus);
              }),
              onChanged: (value) => setState(() => _searchQuery = value),
              textDirection: TextDirection.rtl,
              decoration: InputDecoration(
                hintText: 'جستجو در رسید یا نام مشتری...',
                prefixIcon: Icon(
                  Icons.search_rounded,
                  color: _isSearchFocused ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear_rounded, size: 20),
                        onPressed: () {
                          setState(() => _searchQuery = '');
                          _searchController.clear();
                          FocusScope.of(context).unfocus();
                        },
                      )
                    : null,
                filled: true,
                fillColor: theme.colorScheme.surfaceVariant.withOpacity(0.4),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                   borderRadius: BorderRadius.circular(12),
                   borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
                 ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 38,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 2),
              itemCount: OrderStatus.values.length + 1,
              separatorBuilder: (context, index) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                if (index == 0) {
                  return _buildFilterChip(
                    theme: theme,
                    label: 'همه',
                    isSelected: _selectedStatus == null,
                    onSelected: (_) => setState(() => _selectedStatus = null),
                  );
                }
                final status = OrderStatus.values[index - 1];
                return _buildFilterChip(
                  theme: theme,
                  label: _getStatusText(status),
                  status: status,
                  isSelected: _selectedStatus == status,
                  onSelected: (selected) {
                    setState(() => _selectedStatus = selected ? status : null);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required ThemeData theme,
    required String label,
    required bool isSelected,
    required Function(bool) onSelected,
    OrderStatus? status,
  }) {
    final chipColor = status != null ? _getStatusColor(status, theme) : theme.colorScheme.primary;
    final labelColor = isSelected ? (chipColor.computeLuminance() > 0.5 ? Colors.black : Colors.white) : theme.colorScheme.onSurfaceVariant;
    final backgroundColor = isSelected ? chipColor : theme.colorScheme.surfaceVariant.withOpacity(0.5);
    final borderColor = isSelected ? Colors.transparent : theme.dividerColor;

    return FilterChip(
      label: Text(
        label,
        style: theme.textTheme.labelMedium?.copyWith(color: labelColor, fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
      ),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: backgroundColor,
      selectedColor: chipColor,
      showCheckmark: false,
      side: BorderSide(color: borderColor, width: 1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'در انتظار';
      case OrderStatus.inProgress:
        return 'در حال انجام';
      case OrderStatus.completed:
        return 'تکمیل شده';
      case OrderStatus.cancelled:
        return 'لغو شده';
      case OrderStatus.delivered:
        return 'تحویل داده شده';
    }
  }

  Color _getStatusColor(OrderStatus status, ThemeData theme) {
    switch (status) {
      case OrderStatus.pending:
        return AppColors.statusPending;
      case OrderStatus.inProgress:
        return theme.colorScheme.primary;
      case OrderStatus.completed:
        return theme.colorScheme.tertiary;
      case OrderStatus.cancelled:
        return theme.colorScheme.error;
      case OrderStatus.delivered:
        return Colors.purple.shade600;
    }
  }

  Widget _buildEmptyState(ThemeData theme, TextTheme textTheme) {
    final isFiltering = _searchQuery.isNotEmpty || _selectedStatus != null;
    final title = isFiltering ? 'سفارش مورد نظر یافت نشد' : 'هنوز سفارشی ثبت نشده است';
    final description = isFiltering ? 'لطفا معیار جستجو یا فیلتر را تغییر دهید' : 'برای ثبت سفارش جدید روی دکمه + کلیک کنید';
    final icon = isFiltering ? Icons.search_off_rounded : Icons.receipt_long_outlined;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: theme.colorScheme.secondary.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              description,
              style: textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
            if (isFiltering)
              Padding(
                padding: const EdgeInsets.only(top: 24.0),
                child: TextButton.icon(
                  icon: const Icon(Icons.clear_all_rounded, size: 20),
                  label: const Text('پاک کردن جستجو/فیلتر'),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _searchController.clear();
                      _selectedStatus = null;
                      FocusScope.of(context).unfocus();
                    });
                  },
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, TextTheme textTheme, Object error) {
     return Center(
       child: Padding(
         padding: const EdgeInsets.all(32.0),
         child: Column(
           mainAxisAlignment: MainAxisAlignment.center,
           children: [
             Icon(
               Icons.error_outline_rounded,
               size: 64,
               color: theme.colorScheme.error,
             ),
             const SizedBox(height: 16),
             Text(
               'خطا در بارگیری سفارشات',
               style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
             ),
             const SizedBox(height: 8),
             Text(
               error.toString(),
               style: textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
               textAlign: TextAlign.center,
             ),
             const SizedBox(height: 24),
             TextButton.icon(
                onPressed: () => ref.refresh(ordersProvider.future),
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('تلاش مجدد'),
             )
           ],
         ),
       ),
     );
   }
} 